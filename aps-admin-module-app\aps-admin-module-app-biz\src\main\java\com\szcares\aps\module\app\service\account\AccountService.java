package com.szcares.aps.module.app.service.account;

import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.module.app.controller.admin.account.vo.*;
import com.szcares.aps.module.app.dal.dataobject.account.AccountDO;

import java.util.List;

public interface AccountService {

    String createAccount(AccountSaveReqVO createReqVO);

    void updateAccount(AccountSaveReqVO updateReqVO);

    void updateAccountPwd(AccountSaveReqVO updateReqVO);

    void deleteAccount(String id);

    AccountDO getAccount(String id);

    PageResult<AccountDO> getAccountPageList(AccountPageReqVO reqVO);

    List<AccountDO> getAccountList(AccountListReqVO reqVO);

    List<AccountDO> getSimpleAccountListByTenant(AccountListByTenantReqVO reqVO);

}
