package com.szcares.aps.module.app.dal.dataobject.pricing;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@TableName("pricing_rules_extend")
@Data
public class PricingRuleDO {
    private Long id;
    private String provider;
    private String modelName;
    private String usageMode;
    private BigDecimal inputPrice;
    private BigDecimal outputPrice;
    private BigDecimal imagePrice;
    private BigDecimal markupRate;
    private String currency;
    private LocalDateTime effectiveDate;
    private LocalDateTime expiryDate;
    private String createdBy;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String tenantId;
    private Integer status;
}