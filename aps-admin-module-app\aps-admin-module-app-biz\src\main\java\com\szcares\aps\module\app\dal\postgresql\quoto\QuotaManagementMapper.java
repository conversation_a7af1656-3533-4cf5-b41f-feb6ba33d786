package com.szcares.aps.module.app.dal.postgresql.quoto;

import com.szcares.aps.framework.mybatis.core.mapper.BaseMapperX;
import com.szcares.aps.module.app.controller.admin.quoto.vo.GetAccountQuotaRankingDataReq;
import com.szcares.aps.module.app.controller.admin.quoto.vo.GetQuotaManagementDataResponse;
import com.szcares.aps.module.app.dal.dataobject.quoto.QuotoDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface QuotaManagementMapper extends BaseMapperX<QuotoDO> {

    @Select("SELECT * FROM account_money_extend WHERE account_id = CAST(#{id} AS uuid)")
    QuotoDO selectById(@Param("id") String id);

    @Update("UPDATE account_money_extend SET " +
            "total_quota = #{entity.totalQuota}, " +
            "updated_at = NOW() " +
            "WHERE account_id = #{entity.accountId}::uuid")
    int customUpdateById(@Param("entity") QuotoDO entity);

    @Insert("insert INTO public.account_money_extend " +
            "( id,account_id,total_quota,used_quota,created_at,updated_at)" +
            "VALUES( #{entity.id}::uuid, #{entity.accountId}::uuid, #{entity.totalQuota}, #{entity.usedQuota}, NOW(), NOW() )" )
    int insert(@Param("entity") QuotoDO entity);


    @Select({
            "<script>",
            "SELECT",
            "   ame.account_id,",
            "   RANK() OVER (ORDER BY ame.used_quota DESC) AS ranking,",
            "   a.name AS account_name,",
            "   ame.used_quota,",
            "   ame.total_quota",
            "FROM public.account_money_extend ame",
            "JOIN accounts a ON ame.account_id = a.id",
            "<where>",
            "   <if test='req.keyword != null and req.keyword.trim() != \"\"'>",
            "       <bind name='likeKeyword' value=\"'%' + keyword + '%'\" />",
            "       a.name LIKE #{likeKeyword} OR a.email LIKE #{likeKeyword}",
            "   </if>",
            "</where>",
            "ORDER BY ranking",
            "LIMIT #{req.pageSize} OFFSET #{offset}",
            "</script>"
    })
    List<GetQuotaManagementDataResponse> findResult(@Param("req") GetAccountQuotaRankingDataReq req
    , @Param("offset") int offset);


    @Select({
            "<script>",
            "SELECT COUNT(*) FROM public.account_money_extend ame JOIN accounts a ON ame.account_id = a.id WHERE 1=1",
            "<if test='req.keyword != null and req.keyword != \"\"'>",
            "AND (a.name ILIKE '%' || #{req.keyword} || '%' OR a.email ILIKE '%' || #{req.keyword} || '%')",
            "</if>",
            "</script>"
    })
    Long countResult(@Param("req") GetAccountQuotaRankingDataReq req);

}

