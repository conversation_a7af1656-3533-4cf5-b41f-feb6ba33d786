package com.szcares.aps.module.app.service.tenants;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.annotations.VisibleForTesting;
import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.framework.common.util.object.BeanUtils;
import com.szcares.aps.module.app.controller.admin.tenants.vo.TenantsAccountReqVO;
import com.szcares.aps.module.app.controller.admin.tenants.vo.TenantsAccountUnassignVO;
import com.szcares.aps.module.app.controller.admin.tenants.vo.TenantsListReqVO;
import com.szcares.aps.module.app.controller.admin.tenants.vo.TenantsSaveReqVO;
import com.szcares.aps.module.app.dal.dataobject.tenantQuoto.TenantQuotaDO;
import com.szcares.aps.module.app.dal.dataobject.tenants.TenantsAccountJoinsDO;
import com.szcares.aps.module.app.dal.dataobject.tenants.TenantsDO;
import com.szcares.aps.module.app.dal.postgresql.tenantQuoto.TenantQuotaManagementMapper;
import com.szcares.aps.module.app.dal.postgresql.tenants.TenantsAccountJoinsMapper;
import com.szcares.aps.module.app.dal.postgresql.tenants.TenantsMapper;
import com.szcares.aps.module.app.util.RsaUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static com.szcares.aps.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.szcares.aps.module.system.enums.ErrorCodeConstants.*;

/**
 * 租户 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TenantsServiceImpl implements TenantsService {

    @Resource
    private TenantsMapper TenantsMapper;

    @Resource
    private TenantQuotaManagementMapper tenantQuotaManagementMapper;

    @Resource
    private TenantsAccountJoinsMapper JoinsMapper;

    @Value("${aps-admin.rsapath}")
    private String rsapath;

    @Override
    @DS("apsds")
    @Transactional
    public String createTenants(TenantsSaveReqVO createReqVO) {
        // 校验租户名的唯一性
        validateTenantsNameUnique(null, createReqVO.getName());

        // 插入租户
        TenantsDO tenant = BeanUtils.toBean(createReqVO, TenantsDO.class);

        String uuid = java.util.UUID.randomUUID().toString();

        //生成租户公钥
        String encryptPublicKey = RsaUtil.generateKeyPair(uuid, rsapath);

        tenant.setId(uuid);
        tenant.setEncryptPublicKey(encryptPublicKey);
        tenant.setPlan("basic");
        tenant.setStatus("normal");

        TenantsMapper.insertByTenantsDO(tenant);

        TenantQuotaDO tenantQuotaDO = new TenantQuotaDO();
        tenantQuotaDO.setId(java.util.UUID.randomUUID().toString());
        tenantQuotaDO.setTenantId(uuid);
        tenantQuotaDO.setTotalQuota(createReqVO.getTotalQuota());
        tenantQuotaDO.setUsedQuota(new BigDecimal("0"));

        tenantQuotaManagementMapper.insert(tenantQuotaDO);

        return tenant.getId();
    }

    @Override
    @DS("apsds")
    public void updateTenants(TenantsSaveReqVO updateReqVO) {
        // 校验自己存在
        validateTenantsExists(updateReqVO.getId());
        // 校验租户名的唯一性
        validateTenantsNameUnique(updateReqVO.getId(), updateReqVO.getName());

        // 更新租户
        TenantsDO updateObj = BeanUtils.toBean(updateReqVO, TenantsDO.class);
        updateObj.setUpdatedAt(LocalDateTime.now());
        int rows = TenantsMapper.updateByTenantsDO(updateObj);
    }

    @Override
    @DS("apsds")
    public void deleteTenants(String id) {
        // 校验是否存在
        validateTenantsExists(id);
        TenantsDO updateObj = new TenantsDO();
        updateObj.setId(id);
        updateObj.setUpdatedAt(LocalDateTime.now());
        updateObj.setStatus("invalid");
        // 删除租户
        int rows = TenantsMapper.updateStatusById(updateObj);
    }

    @VisibleForTesting
    @DS("apsds")
    void validateTenantsExists(String id) {
        if (id == null) {
            return;
        }
        TenantsDO Tenants = TenantsMapper.selectById(id);
        if (Tenants == null) {
            throw exception(DEPT_NOT_FOUND);
        }
    }

    @VisibleForTesting
    @DS("apsds")
    void validateTenantsNameUnique(String id, String name) {
        TenantsDO Tenants = TenantsMapper.selectByNameAndStatus(name, "normal");
        if (Tenants == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的租户
        if (id == null) {
            throw exception(DEPT_NAME_DUPLICATE);
        }
        if (ObjectUtil.notEqual(Tenants.getId(), id)) {
            throw exception(DEPT_NAME_DUPLICATE);
        }
    }

    @Override
    @DS("apsds")
    public TenantsDO getTenants(String id) {
        return TenantsMapper.selectById(id);
    }

    @Override
    @DS("apsds")
    public PageResult<TenantsDO> getTenantsPageList(TenantsListReqVO reqVO) {
        reqVO.setStatus("normal");
        PageResult<TenantsDO> list = TenantsMapper.selectPage(reqVO);
        return list;
    }

    @Override
    @DS("apsds")
    public List<TenantsDO> getTenantsList(TenantsListReqVO reqVO) {
        reqVO.setStatus("normal");
        List<TenantsDO> list = TenantsMapper.selectList(reqVO);
        return list;
    }

    @Override
    @DS("apsds")
    public String bindUserToTenants(TenantsAccountReqVO reqVO) {

        TenantsAccountJoinsDO tenantsAccountJoinsDO = JoinsMapper.selectByRoleAndTenantId(reqVO.getTenantId());
        if (tenantsAccountJoinsDO != null && reqVO.getRole().equals("owner")) {
            throw exception(USER_ONLY_ONE_OWNER);
        }
        TenantsAccountJoinsDO joinsDO = new TenantsAccountJoinsDO();
        joinsDO.setId(java.util.UUID.randomUUID().toString());
        joinsDO.setTenantId(reqVO.getTenantId());
        joinsDO.setAccountId(reqVO.getAccountId());
        joinsDO.setRole(reqVO.getRole());
        joinsDO.setInvitedBy(reqVO.getInvitedBy());
        JoinsMapper.insertJoinsDO(joinsDO);
        return joinsDO.getId();
    }

    @Override
    @DS("apsds")
    public void unassignUserToTenants(TenantsAccountUnassignVO reqVO) {

        JoinsMapper.deleteByUUId(reqVO.getId());
    }

}
