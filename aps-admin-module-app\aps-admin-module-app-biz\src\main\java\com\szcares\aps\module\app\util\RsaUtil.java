package com.szcares.aps.module.app.util;

import org.springframework.beans.factory.annotation.Value;

import java.io.File;
import java.io.FileOutputStream;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.util.Base64;

public class RsaUtil {

    /**
     * 生成RSA密钥对，并保存私钥到本地，返回公钥字符串
     */
    public static String generateKeyPair(String tenantId,String rsapath) {

        try {
            // 生成密钥对
            KeyPairGenerator keyGen = KeyPairGenerator.getInstance("RSA");
            keyGen.initialize(2048);
            KeyPair keyPair = keyGen.generateKeyPair();

            RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
            RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();

            String pemPrivate = toPEM(privateKey, "PRIVATE KEY");
            String pemPublic = toPEM(publicKey, "PUBLIC KEY");

            // 保存私钥到 D:/privkeys/{tenantId}/private.pem
            String dirPath = rsapath + tenantId;
            File dir = new File(dirPath);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String privKeyPath = dirPath + "/private.pem";
            try (FileOutputStream fos = new FileOutputStream(privKeyPath)) {
                fos.write(pemPrivate.getBytes());
            }
            // 返回Base64编码的公钥字符串
//            return Base64.getEncoder().encodeToString(publicKey.getEncoded());
            return pemPublic;
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate RSA key pair", e);
        }
    }

    private static String toPEM(java.security.Key key, String type) {
        StringBuilder sb = new StringBuilder();
        sb.append("-----BEGIN ").append(type).append("-----\n");
        sb.append(Base64.getMimeEncoder(64, "\n".getBytes()).encodeToString(key.getEncoded()));
        sb.append("\n-----END ").append(type).append("-----\n");
        return sb.toString();
    }

}