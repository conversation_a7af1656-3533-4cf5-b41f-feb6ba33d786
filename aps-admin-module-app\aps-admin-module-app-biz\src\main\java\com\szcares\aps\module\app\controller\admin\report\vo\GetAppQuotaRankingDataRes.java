package com.szcares.aps.module.app.controller.admin.report.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "管理后台 - 获取应用配额排名数据的响应 Response VO")
public class GetAppQuotaRankingDataRes {

    private int ranking;          // 排名
    private String appId;         // 应用ID
    private double totalCost;     // 总花费
    private double messageCost;   // 消息花费
    private double workflowCost;  // 工作流花费
    private double recordNum;     // 记录数
    private String name;          // 应用名称
    private String mode;          // 应用类型
    private String accountName;   // 账号名称
    private int useNum;           // 使用次数

}
