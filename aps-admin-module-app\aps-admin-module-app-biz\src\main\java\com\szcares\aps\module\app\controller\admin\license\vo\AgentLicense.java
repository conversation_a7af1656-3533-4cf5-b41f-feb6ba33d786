package com.szcares.aps.module.app.controller.admin.license.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Schema(description = "智能体授权码实体")
public class AgentLicense implements Serializable {
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "智能体名称")
    private String agentName;

    @Schema(description = "智能体唯一标识")
    private String agentId;

    @Schema(description = "授权码")
    private String licenseCode;

    @Schema(description = "状态：0-未使用 1-已使用 2-已过期")
    private Integer status;

    @Schema(description = "到期时间")
    private LocalDateTime expireTime;

    @Schema(description = "生成时间")
    private LocalDateTime createTime;

    @Schema(description = "使用者ID")
    private String userId;

    @Schema(description = "使用时间")
    private LocalDateTime useTime;

    @Schema(description = "操作人")
    private String operator;
}
