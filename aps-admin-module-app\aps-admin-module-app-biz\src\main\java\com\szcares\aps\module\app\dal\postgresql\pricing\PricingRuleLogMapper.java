package com.szcares.aps.module.app.dal.postgresql.pricing;

import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.framework.mybatis.core.mapper.BaseMapperX;
import com.szcares.aps.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.szcares.aps.module.app.controller.admin.pricing.vo.PricingRuleLogPageReqVO;
import com.szcares.aps.module.app.dal.dataobject.pricing.PricingRuleLogDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface PricingRuleLogMapper extends BaseMapperX<PricingRuleLogDO> {
    @Insert("INSERT INTO pricing_rule_log (operator, operate_time, operate_type, operate_content) " +
            "VALUES (#{operator}, #{operateTime}, #{operateType}, #{operateContent})")
    int insert(PricingRuleLogDO log);

    default PageResult<PricingRuleLogDO> selectPage(PricingRuleLogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PricingRuleLogDO>()
                .likeIfPresent(PricingRuleLogDO::getOperator, reqVO.getOperator())
                .likeIfPresent(PricingRuleLogDO::getOperateType, reqVO.getOperateType())
                .orderByDesc(PricingRuleLogDO::getOperator));
    }

}