package com.szcares.aps.module.app.controller.admin.report;


import com.szcares.aps.framework.common.pojo.CommonResult;
import com.szcares.aps.module.app.controller.admin.quoto.vo.GetQuotaManagementDataResponse;
import com.szcares.aps.module.app.controller.admin.report.vo.*;
import com.szcares.aps.module.app.controller.admin.quoto.vo.PageResult;
import com.szcares.aps.module.app.service.report.ExpenseReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.szcares.aps.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 额度报表")
@RestController
@RequestMapping("/app/report")
@Validated
public class ExpenseReportController {

    @Resource
    ExpenseReportService expenseReportService;

    @GetMapping("/getAccountQuotaRankingData")
    @Operation(summary = "获取账号额度排名")
    public CommonResult<PageResult<GetQuotaManagementDataResponse>> getAccountQuotaRankingData
            ( @ModelAttribute GetRankingDataCommonReq req) {
        PageResult<GetQuotaManagementDataResponse> result = expenseReportService.getAccountQuotaRanking(req);
        return success(result);
    }

    @GetMapping("/getAppQuotaRankingData")
    @Operation(summary = "获取应用配额排名")
    public CommonResult<PageResult<GetAppQuotaRankingDataRes>> getAppQuotaRankingData
            (@ModelAttribute GetRankingDataCommonReq req){
        PageResult<GetAppQuotaRankingDataRes> result = expenseReportService.getAppQuotaRankingData(req);
        return success(result);
    }

    @GetMapping("/getAppTokenQuotaRankingData")
    @Operation(summary = "获取应用密钥配额排名")
    public CommonResult<PageResult<GetAppTokenQuotaRankingDataRes>> getAppTokenQuotaRankingData
            (@ModelAttribute GetRankingDataCommonReq req){
        PageResult<GetAppTokenQuotaRankingDataRes> result = expenseReportService.getAppTokenQuotaRankingData(req);
        return success(result);
    }

    @GetMapping("/getAppTokenDailyQuotaData")
    @Operation(summary = "获取每天密钥花费数据")
    public CommonResult<List<GetAppTokenDailyQuotaDataRes>> getAppTokenDailyQuotaData
            (){
        List<GetAppTokenDailyQuotaDataRes> result = expenseReportService.getAppTokenDailyQuotaData();
        return success(result);
    }

    @GetMapping("/getAiImageQuotaRankingData")
    @Operation(summary = "取每天ai图片花费数据")
    public CommonResult<PageResult<GetAiImageQuotaRankingRes>> getAiImageQuotaRankingData
            (@ModelAttribute GetAiImageQuotaRankingDataReq req){
        PageResult<GetAiImageQuotaRankingRes> result = expenseReportService.getAiImageQuotaRankingData(req);
        return success(result);
    }


    @GetMapping("/getMessageExportData")
    @Operation(summary = "导出数据报表")
    ResponseEntity<String> getMessageExportData(
            @RequestParam(required = false) String tenantName,
            HttpServletResponse response){
        List<MessageExportDTO> messages = expenseReportService.getMessagesForExport(tenantName);
        // 检查数据是否为空
        if (messages == null || messages.isEmpty()) {
            return ResponseEntity.ok("数据为空");
        }
        expenseReportService.exportToExcel(messages, response);

        return ResponseEntity.ok().build();
    }

}
