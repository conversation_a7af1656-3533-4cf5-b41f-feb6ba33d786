package com.szcares.aps.framework.signature.config;

import com.szcares.aps.framework.redis.config.ApsAdminRedisAutoConfiguration;
import com.szcares.aps.framework.signature.core.aop.ApiSignatureAspect;
import com.szcares.aps.framework.signature.core.redis.ApiSignatureRedisDAO;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * HTTP API 签名的自动配置类
 *
 * <AUTHOR>
 */
@AutoConfiguration(after = ApsAdminRedisAutoConfiguration.class)
public class ApsAdminApiSignatureAutoConfiguration {

    @Bean
    public ApiSignatureAspect signatureAspect(ApiSignatureRedisDAO signatureRedisDAO) {
        return new ApiSignatureAspect(signatureRedisDAO);
    }

    @Bean
    public ApiSignatureRedisDAO signatureRedisDAO(StringRedisTemplate stringRedisTemplate) {
        return new ApiSignatureRedisDAO(stringRedisTemplate);
    }

}
