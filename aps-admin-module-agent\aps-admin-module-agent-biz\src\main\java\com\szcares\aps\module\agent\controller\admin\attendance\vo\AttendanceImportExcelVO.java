package com.szcares.aps.module.agent.controller.admin.attendance.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ExcelIgnoreUnannotated
public class AttendanceImportExcelVO implements Serializable {

    // 基本信息相关
    @ExcelProperty(index = 0)
    private String employeeName;

    @ExcelProperty(index = 1)
    private String attendanceGroup;

    @ExcelProperty(index = 2)
    private String department;

    @ExcelProperty(index = 3)
    private String userId;

    // 考勤日期相关
    @ExcelProperty(index = 4)
    private String attendanceDate;

    @ExcelProperty(index = 5)
    private String workDate;

    // 班次与打卡信息相关
    @ExcelProperty(index = 6)
    private String shift;

    @ExcelProperty(index = 7)
    private String work1CheckinTime;

    @ExcelProperty(index = 8)
    private String work1CheckinResult;

    @ExcelProperty(index = 9)
    private String work1CheckoutTime;

    @ExcelProperty(index = 10)
    private String work1CheckoutResult;

    @ExcelProperty(index = 11)
    private String work2CheckinTime;

    @ExcelProperty(index = 12)
    private String work2CheckinResult;

    @ExcelProperty(index = 13)
    private String work2CheckoutTime;

    @ExcelProperty(index = 14)
    private String work2CheckoutResult;

    @ExcelProperty(index = 15)
    private String work3CheckinTime;

    @ExcelProperty(index = 16)
    private String work3CheckinResult;

    @ExcelProperty(index = 17)
    private String work3CheckoutTime;

    @ExcelProperty(index = 18)
    private String work3CheckoutResult;

    // 审批与时长相关
    @ExcelProperty(index = 19)
    private String relatedApprovalForm;

    @ExcelProperty(index = 20)
    private String businessTripDuration;

    @ExcelProperty(index = 21)
    private String outingDuration;

    // 请假天数相关
    @ExcelProperty(index = 22)
    private String sickLeaveDays;

    @ExcelProperty(index = 23)
    private String annualLeaveDays;

    @ExcelProperty(index = 24)
    private String personalLeaveDays;

    @ExcelProperty(index = 25)
    private String mourningLeaveDays;

    @ExcelProperty(index = 26)
    private String compensatoryLeaveDays;

    @ExcelProperty(index = 27)
    private String homeVisitLeaveDays;

    @ExcelProperty(index = 28)
    private String maternityLeaveDays;

    @ExcelProperty(index = 29)
    private String paternityLeaveDays;

    @ExcelProperty(index = 30)
    private String marriageLeaveDays;

    @ExcelProperty(index = 31)
    private String nursingLeaveDays;

    @ExcelProperty(index = 32)
    private String childcareLeaveDays;

    @ExcelProperty(index = 33)
    private String careLeaveDays;

    @ExcelProperty(index = 34)
    private String menstrualLeaveDays;

    // 加班相关
    @ExcelProperty(index = 35)
    private String totalOvertimeDuration;

    @ExcelProperty(index = 36)
    private String workingDayovertimePay;

    @ExcelProperty(index = 37)
    private String restDayovertimePay;

    @ExcelProperty(index = 38)
    private String holidayovertimePay;

    @ExcelProperty(index = 39)
    private String workingDayCompensatoryLeave;

    @ExcelProperty(index = 40)
    private String restDayCompensatoryLeave;

    @ExcelProperty(index = 41)
    private String holidayCompensatoryLeave;
}