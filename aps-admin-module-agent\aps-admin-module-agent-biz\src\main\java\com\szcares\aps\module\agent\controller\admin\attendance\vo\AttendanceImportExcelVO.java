package com.szcares.aps.module.agent.controller.admin.attendance.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AttendanceImportExcelVO implements Serializable {

    // 基本信息相关
    @ExcelProperty(value = "姓名", index = 0)
    private String employeeName;

    @ExcelProperty(value = "考勤组", index = 1)
    private String attendanceGroup;

    @ExcelProperty(value = "部门", index = 2)
    private String department;

    @ExcelProperty(value = "UserId", index = 3)
    private String userId;

    // 考勤日期相关
    @ExcelProperty(value = "日期", index = 4)
    private String attendanceDate;

    @ExcelProperty(value = "workDate", index = 5)
    private String workDate;

    // 班次与打卡信息相关
    @ExcelProperty(value = "班次", index = 6)
    private String shift;

    @ExcelProperty(value = "上班1打卡时间", index = 7)
    private String work1CheckinTime;

    @ExcelProperty(value = "上班1打卡结果", index = 8)
    private String work1CheckinResult;

    @ExcelProperty(value = "下班1打卡时间", index = 9)
    private String work1CheckoutTime;

    @ExcelProperty(value = "下班1打卡结果", index = 10)
    private String work1CheckoutResult;

    @ExcelProperty(value = "上班2打卡时间", index = 11)
    private String work2CheckinTime;

    @ExcelProperty(value = "上班2打卡结果", index = 12)
    private String work2CheckinResult;

    @ExcelProperty(value = "下班2打卡时间", index = 13)
    private String work2CheckoutTime;

    @ExcelProperty(value = "下班2打卡结果", index = 14)
    private String work2CheckoutResult;

    @ExcelProperty(value = "上班3打卡时间", index = 15)
    private String work3CheckinTime;

    @ExcelProperty(value = "上班3打卡结果", index = 16)
    private String work3CheckinResult;

    @ExcelProperty(value = "下班3打卡时间", index = 17)
    private String work3CheckoutTime;

    @ExcelProperty(value = "下班3打卡结果", index = 18)
    private String work3CheckoutResult;

    // 审批与时长相关
    @ExcelProperty(value = "关联的审批单", index = 19)
    private String relatedApprovalForm;

    @ExcelProperty(value = "出差时长", index = 20)
    private String businessTripDuration;

    @ExcelProperty(value = "外出时长", index = 21)
    private String outingDuration;

    // 请假天数相关
    @ExcelProperty(value = {"请假", "病假(天)"}, index = 22)
    private String sickLeaveDays;

    @ExcelProperty(value = {"请假", "年假(天)"}, index = 23)
    private String annualLeaveDays;

    @ExcelProperty(value = {"请假", "事假(天)"}, index = 24)
    private String personalLeaveDays;

    @ExcelProperty(value = {"请假", "丧假(天)"}, index = 25)
    private String mourningLeaveDays;

    @ExcelProperty(value = {"请假", "调休(天)"}, index = 26)
    private String compensatoryLeaveDays;

    @ExcelProperty(value = {"请假", "探亲假(天)"}, index = 27)
    private String homeVisitLeaveDays;

    @ExcelProperty(value = {"请假", "产假(天)"}, index = 28)
    private String maternityLeaveDays;

    @ExcelProperty(value = {"请假", "陪产假(天)"}, index = 29)
    private String paternityLeaveDays;

    @ExcelProperty(value = {"请假", "婚假(天)"}, index = 30)
    private String marriageLeaveDays;

    @ExcelProperty(value = {"请假", "哺乳假(天)"}, index = 31)
    private String nursingLeaveDays;

    @ExcelProperty(value = {"请假", "育儿假(天)"}, index = 32)
    private String childcareLeaveDays;

    @ExcelProperty(value = {"请假", "护理假(天)"}, index = 33)
    private String careLeaveDays;

    @ExcelProperty(value = {"请假", "例假(天)"}, index = 34)
    private String menstrualLeaveDays;

    // 加班相关
    @ExcelProperty(value = "加班总时长", index = 35)
    private String totalOvertimeDuration;

    @ExcelProperty(value = {"加班时长（转加班费）", "工作日（转加班费）"}, index = 36)
    private String workingDayovertimePay;

    @ExcelProperty(value = {"加班时长（转加班费）", "休息日（转加班费）"}, index = 37)
    private String restDayovertimePay;

    @ExcelProperty(value = {"加班时长（转加班费）", "节假日（转加班费）"}, index = 38)
    private String holidayovertimePay;

    @ExcelProperty(value = {"加班时长（转调休）", "工作日（转调休）"}, index = 39)
    private String workingDayCompensatoryLeave;

    @ExcelProperty(value = {"加班时长（转调休）", "休息日（转调休）"}, index = 40)
    private String restDayCompensatoryLeave;

    @ExcelProperty(value = {"加班时长（转调休）", "节假日（转调休）"}, index = 41)
    private String holidayCompensatoryLeave;
}