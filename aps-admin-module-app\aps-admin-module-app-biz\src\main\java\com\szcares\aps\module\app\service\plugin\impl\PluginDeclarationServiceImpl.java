package com.szcares.aps.module.app.service.plugin.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.szcares.aps.module.app.controller.admin.pricing.vo.PluginModelVO;
import com.szcares.aps.module.app.dal.postgresql.plugin.PluginDeclarationMapper;
import com.szcares.aps.module.app.service.plugin.PluginDeclarationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class PluginDeclarationServiceImpl implements PluginDeclarationService {

    @Resource
    private PluginDeclarationMapper pluginDeclarationMapper;

    // Jackson 的 JSON 解析器
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 查询插件模型列表
     * @return 插件模型VO列表
     */
    @DS("pluginds")
    @Override
    @Transactional
    public List<PluginModelVO> getPluginModelList() {
        // 从数据库查询所有 plugin_id 和 declaration 字段
        List<Map<String, Object>> dbList = pluginDeclarationMapper.selectAll();
        List<PluginModelVO> result = new ArrayList<>();
        // 遍历每一条数据库记录
        for (Map<String, Object> row : dbList) {
            String pluginId = (String) row.get("plugin_id");
            String declaration = (String) row.get("declaration");
            try {
                // 解析 declaration 字段为 JSON
                JsonNode root = objectMapper.readTree(declaration);
                // 拼接 provider 字段（格式：pluginId/name）
                String provider = pluginId + "/" + root.path("name").asText();
                // 获取 models 数组
                JsonNode models = root.path("model").path("models");
                if (models.isArray()) {
                    // 遍历 models
                    for (JsonNode model : models) {
                        JsonNode pricing = model.path("pricing");
                        // 跳过没有价格信息的数据
                        if (pricing.isMissingNode() ||
                            (pricing.path("input").isMissingNode() || pricing.path("input").asText().isEmpty())
                            && (pricing.path("output").isMissingNode() || pricing.path("output").asText().isEmpty())
                            && (pricing.path("image").isMissingNode() || pricing.path("image").asText().isEmpty())
                        ) {
                            continue;
                        }
                        // 封装 PluginModelVO
                        PluginModelVO vo = new PluginModelVO();
                        vo.setProvider(provider);
                        vo.setModelName(model.path("model").asText());
                        vo.setUsageMode(model.path("model_type").asText());
                        vo.setInputPrice(pricing.path("input").asText(""));
                        vo.setOutputPrice(pricing.path("output").asText(""));
                        vo.setImagePrice(pricing.path("image").asText(""));
                        vo.setCurrency(pricing.path("currency").asText(""));
                        result.add(vo);
                    }
                }
            } catch (Exception e) {
                log.warn("解析 plugin_declarations 失败，plugin_id={}, 错误信息={}", pluginId, e.getMessage());
            }
        }
        return result;
    }
}