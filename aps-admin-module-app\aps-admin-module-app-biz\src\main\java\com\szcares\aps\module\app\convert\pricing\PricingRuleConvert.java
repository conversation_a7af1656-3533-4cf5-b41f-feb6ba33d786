package com.szcares.aps.module.app.convert.pricing;

import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.module.app.controller.admin.pricing.vo.PricingRuleRespVO;
import com.szcares.aps.module.app.controller.admin.pricing.vo.PricingRuleSaveReqVO;
import com.szcares.aps.module.app.controller.admin.pricing.vo.PricingRuleUpdateReqVO;
import com.szcares.aps.module.app.dal.dataobject.pricing.PricingRuleDO;

public class PricingRuleConvert {

    public static PricingRuleDO convertPricingRuleDO(PricingRuleSaveReqVO bean){
        if (bean == null) {
            return null;
        }
        PricingRuleDO entity = new PricingRuleDO();
        entity.setProvider(bean.getProvider());
        entity.setModelName(bean.getModelName());
        entity.setUsageMode(bean.getUsageMode());
        entity.setInputPrice(bean.getInputPrice());
        entity.setOutputPrice(bean.getOutputPrice());
        entity.setImagePrice(bean.getImagePrice());
        entity.setMarkupRate(bean.getMarkupRate());
        entity.setCurrency(bean.getCurrency());
        entity.setEffectiveDate(bean.getEffectiveDate());
        entity.setExpiryDate(bean.getExpiryDate());
        entity.setTenantId(bean.getTenantId());
        entity.setStatus(bean.getStatus());
        return entity;
    };

    public static PricingRuleDO convertPricingRuleDO(PricingRuleUpdateReqVO bean){
        if (bean == null) {
            return null;
        }
        PricingRuleDO entity = new PricingRuleDO();
        entity.setId(bean.getId());
        entity.setProvider(bean.getProvider());
        entity.setModelName(bean.getModelName());
        entity.setUsageMode(bean.getUsageMode());
        entity.setInputPrice(bean.getInputPrice());
        entity.setOutputPrice(bean.getOutputPrice());
        entity.setImagePrice(bean.getImagePrice());
        entity.setMarkupRate(bean.getMarkupRate());
        entity.setCurrency(bean.getCurrency());
        entity.setEffectiveDate(bean.getEffectiveDate());
        entity.setExpiryDate(bean.getExpiryDate());
        entity.setTenantId(bean.getTenantId());
        entity.setStatus(bean.getStatus());
        return entity;
    };

    public static PricingRuleRespVO convertPricingRuleRespVO(PricingRuleDO bean){
        if (bean == null) {
            return null;
        }
        PricingRuleRespVO entity = new PricingRuleRespVO();
        entity.setId(bean.getId());
        entity.setProvider(bean.getProvider());
        entity.setModelName(bean.getModelName());
        entity.setUsageMode(bean.getUsageMode());
        entity.setInputPrice(bean.getInputPrice());
        entity.setOutputPrice(bean.getOutputPrice());
        entity.setImagePrice(bean.getImagePrice());
        entity.setMarkupRate(bean.getMarkupRate());
        entity.setCurrency(bean.getCurrency());
        entity.setEffectiveDate(bean.getEffectiveDate());
        entity.setExpiryDate(bean.getExpiryDate());
        entity.setTenantId(bean.getTenantId());
        entity.setStatus(bean.getStatus());
        entity.setCreatedAt(bean.getCreatedAt());
        entity.setUpdatedAt(bean.getUpdatedAt());
        entity.setCreatedBy(bean.getCreatedBy());
        return entity;
    };

    public static PageResult<PricingRuleRespVO> convertPricingRuleRespVO(PageResult<PricingRuleDO> page) {
        PageResult<PricingRuleRespVO> result = new PageResult<>();
        result.setList(page.getList().stream().map(PricingRuleConvert::convertPricingRuleRespVO).toList());
        result.setTotal(page.getTotal());
        return result;
    }
}