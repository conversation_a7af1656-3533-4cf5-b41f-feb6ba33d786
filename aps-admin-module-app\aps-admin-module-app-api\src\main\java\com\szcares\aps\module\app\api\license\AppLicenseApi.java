package com.szcares.aps.module.app.api.license;

import com.szcares.aps.framework.common.pojo.CommonResult;
import com.szcares.aps.module.app.api.license.dto.AppLicenseRespDTO;
import com.szcares.aps.module.app.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10 14:11
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 应用授权")
public interface AppLicenseApi {

    String PREFIX = ApiConstants.PREFIX + "/license";

    @PostMapping(PREFIX + "/notify")
    @Operation(summary = "通知启用授权码")
    @Parameters({
            @Parameter(name = "tenantsId", description = "租户编号", required = true),
            @Parameter(name = "jwtToken", description = "签名", required = true),
            @Parameter(name = "ApiKey", description = "API 密钥", required = true, in = ParameterIn.HEADER)
    })
    CommonResult<AppLicenseRespDTO> getLicense(
            @RequestParam("tenantsId") String tenantsId,
            @RequestParam("jwtToken") String jwtToken,
            @RequestHeader("ApiKey") String apiKey);

    @GetMapping(PREFIX + "/list")
    @Operation(summary = "获得租户授权码列表")
    @Parameters({
            @Parameter(name = "tenantsId", description = "租户编号", required = true),
            @Parameter(name = "ApiKey", description = "API 密钥", required = true, in = ParameterIn.HEADER)
    })
    CommonResult<List<AppLicenseRespDTO>> getLicensePage(
            @RequestParam("tenantsId") String tenantsId,
            @RequestHeader("ApiKey") String apiKey);
}