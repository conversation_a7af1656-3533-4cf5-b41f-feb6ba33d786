package com.szcares.aps.module.app.service.tenantQuoto;

import com.szcares.aps.module.app.controller.admin.tenantQuoto.vo.PageResult;
import com.szcares.aps.module.app.controller.admin.tenantQuoto.vo.GetTenantQuotaManagementDataResponse;
import com.szcares.aps.module.app.controller.admin.tenantQuoto.vo.GetTenantQuotaRankingDataReq;
import com.szcares.aps.module.app.controller.admin.tenantQuoto.vo.SetTenantQuotaReq;

/**
 * 额度管理Service 接口
 *
 * <AUTHOR>
 */

public interface TenantQuotaManagementService {

    /**
     * 设置额度
     *
     * @param req
     * @return
     */
    void setTenantQuota(SetTenantQuotaReq req);


    /**
     * 获取额度列表
     *
     * @param req
     * @return
     */
    PageResult<GetTenantQuotaManagementDataResponse> getTenantQuotaManagementList(GetTenantQuotaRankingDataReq req);
}
