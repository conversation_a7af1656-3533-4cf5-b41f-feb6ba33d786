<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.szcares.aps</groupId>
        <artifactId>aps-admin-module-agent</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>aps-admin-module-agent-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        agent 模块，员工管理相关功能
    </description>

    <dependencies>
        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-env</artifactId>
        </dependency>

        <!-- 依赖服务 -->
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-module-agent-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-biz-ip</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-rpc</artifactId>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-job</artifactId>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-mq</artifactId>
        </dependency>

        <!-- 服务保障相关 -->
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-protection</artifactId>
        </dependency>

        <!-- 监控相关 -->
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-monitor</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-excel</artifactId>
        </dependency>

        <!-- 三方云服务相关 -->
<!--        <dependency>-->
<!--            <groupId>com.szcares.aps</groupId>-->
<!--            <artifactId>aps-admin-spring-boot-starter-file</artifactId>-->
<!--        </dependency>-->

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version> <!-- 如果 spring.boot.version 版本修改，则这里也要跟着修改 -->
                <configuration>
                    <mainClass>com.szcares.aps.module.agent.AgentServerApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>