package com.szcares.aps.module.agent.controller.admin.attendance.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 员工考勤登记表 Response VO")
@Data
public class AttendanceRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "员工姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String employeeName;

    @Schema(description = "考勤组", example = "A组")
    private String attendanceGroup;

    @Schema(description = "所属部门", example = "技术部")
    private String department;

    @Schema(description = "用户ID", example = "001")
    private String userId;

    @Schema(description = "考勤日期", example = "2023-01-01")
    private String attendanceDate;

    @Schema(description = "工作日期相关信息", example = "工作日")
    private String workDate;

    @Schema(description = "班次", example = "早班")
    private String shift;

    @Schema(description = "第一次上班打卡时间", example = "09:00")
    private String work1CheckinTime;

    @Schema(description = "第一次上班打卡结果", example = "正常")
    private String work1CheckinResult;

    @Schema(description = "第一次下班打卡时间", example = "18:00")
    private String work1CheckoutTime;

    @Schema(description = "第一次下班打卡结果", example = "正常")
    private String work1CheckoutResult;

    @Schema(description = "关联的审批单", example = "AP001")
    private String relatedApprovalForm;

    @Schema(description = "出差时长", example = "8")
    private String businessTripDuration;

    @Schema(description = "外出时长", example = "2")
    private String outingDuration;

    @Schema(description = "病假天数", example = "1")
    private String sickLeaveDays;

    @Schema(description = "年假天数", example = "1")
    private String annualLeaveDays;

    @Schema(description = "事假天数", example = "1")
    private String personalLeaveDays;

    @Schema(description = "丧假天数", example = "0")
    private String mourningLeaveDays;

    @Schema(description = "调休天数", example = "0")
    private String compensatoryLeaveDays;

    @Schema(description = "探亲假天数", example = "0")
    private String homeVisitLeaveDays;

    @Schema(description = "产假天数", example = "0")
    private String maternityLeaveDays;

    @Schema(description = "陪产假天数", example = "0")
    private String paternityLeaveDays;

    @Schema(description = "婚假天数", example = "0")
    private String marriageLeaveDays;

    @Schema(description = "哺乳假天数", example = "0")
    private String nursingLeaveDays;

    @Schema(description = "育儿假天数", example = "0")
    private String childcareLeaveDays;

    @Schema(description = "护理假天数", example = "0")
    private String careLeaveDays;

    @Schema(description = "例假天数", example = "0")
    private String menstrualLeaveDays;

    @Schema(description = "加班总时长", example = "2")
    private String totalOvertimeDuration;

    @Schema(description = "工作日（转调休）", example = "0")
    private String workingDayCompensatoryLeave;

    @Schema(description = "休息日（转调休）", example = "0")
    private String restDayCompensatoryLeave;

    @Schema(description = "节假日（转调休）", example = "0")
    private String holidayCompensatoryLeave;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

}