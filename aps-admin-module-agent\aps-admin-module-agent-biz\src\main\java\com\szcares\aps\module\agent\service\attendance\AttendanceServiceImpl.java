package com.szcares.aps.module.agent.service.attendance;

import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.framework.common.util.object.BeanUtils;
import com.szcares.aps.module.agent.controller.admin.attendance.vo.AttendanceExportExcelVO;
import com.szcares.aps.module.agent.controller.admin.attendance.vo.AttendanceImportExcelVO;
import com.szcares.aps.module.agent.controller.admin.attendance.vo.AttendanceImportRespVO;
import com.szcares.aps.module.agent.controller.admin.attendance.vo.AttendancePageReqVO;
import com.szcares.aps.module.agent.controller.admin.attendance.vo.AttendanceRespVO;
import com.szcares.aps.module.agent.controller.admin.attendance.vo.AttendanceSaveReqVO;
import com.szcares.aps.module.agent.convert.attendance.AttendanceConvert;
import com.szcares.aps.module.agent.dal.dataobject.attendance.AttendanceDO;
import com.szcares.aps.module.agent.dal.mysql.attendance.AttendanceMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;

import static com.szcares.aps.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.szcares.aps.module.agent.enums.ErrorCodeConstants.ATTENDANCE_NOT_EXISTS;

/**
 * 员工考勤登记表 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class AttendanceServiceImpl implements AttendanceService {

    @Resource
    private AttendanceMapper attendanceMapper;

    @Override
    public Long createAttendance(AttendanceSaveReqVO createReqVO) {
        AttendanceDO attendance = BeanUtils.toBean(createReqVO, AttendanceDO.class);
        attendanceMapper.insert(attendance);
        return attendance.getId();
    }

    @Override
    public void updateAttendance(AttendanceSaveReqVO updateReqVO) {
        validateAttendanceExists(updateReqVO.getId());
        AttendanceDO updateObj = BeanUtils.toBean(updateReqVO, AttendanceDO.class);
        attendanceMapper.updateById(updateObj);
    }

    @Override
    public void deleteAttendance(Long id) {
        validateAttendanceExists(id);
        attendanceMapper.deleteById(id);
    }

    private void validateAttendanceExists(Long id) {
        if (attendanceMapper.selectById(id) == null) {
            throw exception(ATTENDANCE_NOT_EXISTS);
        }
    }

    @Override
    public AttendanceDO getAttendance(Long id) {
        return attendanceMapper.selectById(id);
    }

    @Override
    public PageResult<AttendanceRespVO> getAttendancePage(AttendancePageReqVO pageReqVO) {
        PageResult<AttendanceDO> pageResult = attendanceMapper.selectPage(pageReqVO);
        return AttendanceConvert.INSTANCE.convertPage(pageResult);
    }

    @Override
    public List<AttendanceDO> getAttendanceList(AttendancePageReqVO exportReqVO) {
        return attendanceMapper.selectList(exportReqVO);
    }

    @Override
    public List<AttendanceExportExcelVO> getAttendanceExportList(AttendancePageReqVO exportReqVO) {
        List<AttendanceDO> list = attendanceMapper.selectList(exportReqVO);
        return AttendanceConvert.INSTANCE.convertList02(list);
    }

    @Override
    public AttendanceImportRespVO importAttendanceList(List<AttendanceImportExcelVO> importReqVO) {
        AttendanceImportRespVO respVO = new AttendanceImportRespVO();
        int createSuccessCount = 0;
        int updateSuccessCount = 0;
        int failureCount = 0;
        List<String> failureMessages = new ArrayList<>();

        log.info("开始导入Excel数据，总条数: {}", importReqVO != null ? importReqVO.size() : 0);
        
        if (importReqVO == null || importReqVO.isEmpty()) {
            log.warn("导入的Excel数据为空");
            failureMessages.add("导入的Excel数据为空");
            respVO.setCreateSuccessCount(0);
            respVO.setUpdateSuccessCount(0);
            respVO.setFailureCount(1);
            respVO.setFailureMessages(failureMessages);
            return respVO;
        }

        int index = 1; // 从1开始计数，符合通常的业务数据条数习惯
        for (AttendanceImportExcelVO excelVO : importReqVO) {
            try {
                log.info("正在处理第{}条数据: 员工姓名={}, 考勤日期={}", 
                        index, excelVO.getEmployeeName(), excelVO.getAttendanceDate());
                
                // 校验必填字段
                if (excelVO.getEmployeeName() == null || excelVO.getEmployeeName().trim().isEmpty()) {
                    log.error("第{}条数据导入失败,员工姓名不能为空", index);
                    failureCount++;
                    failureMessages.add(String.format("第 %d 行：员工姓名不能为空", index));
                    index++;
                    continue;
                }
                if (excelVO.getAttendanceDate() == null || excelVO.getAttendanceDate().trim().isEmpty()) {
                    log.error("第{}条数据导入失败,考勤时间不能为空", index);
                    failureCount++;
                    failureMessages.add(String.format("第 %d 行：考勤时间不能为空", index));
                    index++;
                    continue;
                }

                // 记录打卡时间信息用于调试
                log.debug("第{}条数据打卡时间信息: 上班1={}, 下班1={}, 上班2={}, 下班2={}, 上班3={}, 下班3={}", 
                        index, 
                        excelVO.getWork1CheckinTime(), excelVO.getWork1CheckoutTime(),
                        excelVO.getWork2CheckinTime(), excelVO.getWork2CheckoutTime(),
                        excelVO.getWork3CheckinTime(), excelVO.getWork3CheckoutTime());

                // 处理下班打卡时间的优先级逻辑
                String finalCheckoutTime = determineCheckoutTime(excelVO);
                String finalCheckoutResult = determineCheckoutResult(excelVO);

                log.debug("第{}条数据下班时间处理结果: 最终时间={}, 最终结果={}", index, finalCheckoutTime, finalCheckoutResult);

                // 检查是否已存在（通过员工姓名+考勤日期判断）
                AttendanceDO existingAttendance = attendanceMapper.selectByEmployeeNameAndAttendanceDate(
                        excelVO.getEmployeeName(), excelVO.getAttendanceDate());
                
                if (existingAttendance != null) {
                    // 更新现有记录
                    updateAttendanceFromExcel(existingAttendance, excelVO, finalCheckoutTime, finalCheckoutResult);
                    attendanceMapper.updateById(existingAttendance);
                    updateSuccessCount++;
                    log.info("第{}条数据更新成功: {} - {}", index, excelVO.getEmployeeName(), excelVO.getAttendanceDate());
                } else {
                    // 创建新记录
                    AttendanceDO newAttendance = createAttendanceFromExcel(excelVO, finalCheckoutTime, finalCheckoutResult);
                    attendanceMapper.insert(newAttendance);
                    createSuccessCount++;
                    log.info("第{}条数据创建成功: {} - {}", index, excelVO.getEmployeeName(), excelVO.getAttendanceDate());
                }
            } catch (Exception e) {
                failureCount++;
                String errorMsg = String.format("第 %d 行：处理失败，原因：%s", index, e.getMessage());
                failureMessages.add(errorMsg);
                log.error("第{}条数据导入失败: {} - {}", index, excelVO.getEmployeeName(), excelVO.getAttendanceDate(), e);
            } finally {
                index++; // 每次循环结束后，计数器加1
            }
        }
        
        log.info("Excel导入完成: 成功创建{}条, 成功更新{}条, 失败{}条", 
                createSuccessCount, updateSuccessCount, failureCount);
        
        respVO.setCreateSuccessCount(createSuccessCount);
        respVO.setUpdateSuccessCount(updateSuccessCount);
        respVO.setFailureCount(failureCount);
        respVO.setFailureMessages(failureMessages);
        return respVO;
    }

    /**
     * 根据优先级逻辑确定最终的下班打卡时间
     * 优先级：下班3 > 下班2 > 下班1
     */
    private String determineCheckoutTime(AttendanceImportExcelVO excelVO) {
        // 如果下班3打卡时间不为空，则使用下班3时间
        if (excelVO.getWork3CheckoutTime() != null && !excelVO.getWork3CheckoutTime().trim().isEmpty()) {
            return excelVO.getWork3CheckoutTime();
        }
        
        // 如果下班3时间为空，下班2时间不为空，则使用下班2时间
        if (excelVO.getWork2CheckoutTime() != null && !excelVO.getWork2CheckoutTime().trim().isEmpty()) {
            return excelVO.getWork2CheckoutTime();
        }
        
        // 如果下班3时间和下班2时间都为空，则使用下班1时间
        if (excelVO.getWork1CheckoutTime() != null && !excelVO.getWork1CheckoutTime().trim().isEmpty()) {
            return excelVO.getWork1CheckoutTime();
        }
        
        // 如果所有下班时间都为空，返回null
        return null;
    }

    /**
     * 根据优先级逻辑确定最终的下班打卡结果
     * 优先级：下班3 > 下班2 > 下班1
     */
    private String determineCheckoutResult(AttendanceImportExcelVO excelVO) {
        // 如果下班3打卡时间不为空，则使用下班3结果
        if (excelVO.getWork3CheckoutTime() != null && !excelVO.getWork3CheckoutTime().trim().isEmpty()) {
            return excelVO.getWork3CheckoutResult();
        }
        
        // 如果下班3时间为空，下班2时间不为空，则使用下班2结果
        if (excelVO.getWork2CheckoutTime() != null && !excelVO.getWork2CheckoutTime().trim().isEmpty()) {
            return excelVO.getWork2CheckoutResult();
        }
        
        // 如果下班3时间和下班2时间都为空，则使用下班1结果
        if (excelVO.getWork1CheckoutTime() != null && !excelVO.getWork1CheckoutTime().trim().isEmpty()) {
            return excelVO.getWork1CheckoutResult();
        }
        
        // 如果所有下班时间都为空，返回null
        return null;
    }

    /**
     * 从Excel数据创建考勤记录
     */
    private AttendanceDO createAttendanceFromExcel(AttendanceImportExcelVO excelVO, String finalCheckoutTime, String finalCheckoutResult) {
        AttendanceDO attendance = new AttendanceDO();
        
        // 基本信息
        attendance.setEmployeeName(excelVO.getEmployeeName());
        attendance.setAttendanceGroup(excelVO.getAttendanceGroup());
        attendance.setDepartment(excelVO.getDepartment());
//        attendance.setUserId(excelVO.getUserId());
        attendance.setAttendanceDate(excelVO.getAttendanceDate());
//        attendance.setWorkDate(excelVO.getWorkDate());
        attendance.setShift(excelVO.getShift());
        
        // 最终下班打卡信息（根据优先级逻辑确定）
        attendance.setCheckoutTime(finalCheckoutTime);
        attendance.setCheckoutResult(finalCheckoutResult);
        
        // 其他信息
        attendance.setRelatedApprovalForm(excelVO.getRelatedApprovalForm());
        attendance.setBusinessTripDuration(excelVO.getBusinessTripDuration());
        attendance.setOutingDuration(excelVO.getOutingDuration());
        attendance.setSickLeaveDays(excelVO.getSickLeaveDays());
        attendance.setAnnualLeaveDays(excelVO.getAnnualLeaveDays());
        attendance.setPersonalLeaveDays(excelVO.getPersonalLeaveDays());
        attendance.setMourningLeaveDays(excelVO.getMourningLeaveDays());
        attendance.setCompensatoryLeaveDays(excelVO.getCompensatoryLeaveDays());
        attendance.setHomeVisitLeaveDays(excelVO.getHomeVisitLeaveDays());
        attendance.setMaternityLeaveDays(excelVO.getMaternityLeaveDays());
        attendance.setPaternityLeaveDays(excelVO.getPaternityLeaveDays());
        attendance.setMarriageLeaveDays(excelVO.getMarriageLeaveDays());
        attendance.setNursingLeaveDays(excelVO.getNursingLeaveDays());
        attendance.setChildcareLeaveDays(excelVO.getChildcareLeaveDays());
        attendance.setCareLeaveDays(excelVO.getCareLeaveDays());
        attendance.setMenstrualLeaveDays(excelVO.getMenstrualLeaveDays());
        attendance.setTotalOvertimeDuration(excelVO.getTotalOvertimeDuration());
        attendance.setWorkingDayCompensatoryLeave(excelVO.getWorkingDayCompensatoryLeave());
        attendance.setRestDayCompensatoryLeave(excelVO.getRestDayCompensatoryLeave());
        attendance.setHolidayCompensatoryLeave(excelVO.getHolidayCompensatoryLeave());
        
        return attendance;
    }

    /**
     * 从Excel数据更新考勤记录
     */
    private void updateAttendanceFromExcel(AttendanceDO attendance, AttendanceImportExcelVO excelVO, String finalCheckoutTime, String finalCheckoutResult) {
        // 基本信息
        attendance.setAttendanceGroup(excelVO.getAttendanceGroup());
        attendance.setDepartment(excelVO.getDepartment());
//        attendance.setUserId(excelVO.getUserId());
//        attendance.setWorkDate(excelVO.getWorkDate());
        attendance.setShift(excelVO.getShift());

        // 最终下班打卡信息（根据优先级逻辑确定）
        attendance.setCheckoutTime(finalCheckoutTime);
        attendance.setCheckoutResult(finalCheckoutResult);
        
        // 其他信息
        attendance.setRelatedApprovalForm(excelVO.getRelatedApprovalForm());
        attendance.setBusinessTripDuration(excelVO.getBusinessTripDuration());
        attendance.setOutingDuration(excelVO.getOutingDuration());
        attendance.setSickLeaveDays(excelVO.getSickLeaveDays());
        attendance.setAnnualLeaveDays(excelVO.getAnnualLeaveDays());
        attendance.setPersonalLeaveDays(excelVO.getPersonalLeaveDays());
        attendance.setMourningLeaveDays(excelVO.getMourningLeaveDays());
        attendance.setCompensatoryLeaveDays(excelVO.getCompensatoryLeaveDays());
        attendance.setHomeVisitLeaveDays(excelVO.getHomeVisitLeaveDays());
        attendance.setMaternityLeaveDays(excelVO.getMaternityLeaveDays());
        attendance.setPaternityLeaveDays(excelVO.getPaternityLeaveDays());
        attendance.setMarriageLeaveDays(excelVO.getMarriageLeaveDays());
        attendance.setNursingLeaveDays(excelVO.getNursingLeaveDays());
        attendance.setChildcareLeaveDays(excelVO.getChildcareLeaveDays());
        attendance.setCareLeaveDays(excelVO.getCareLeaveDays());
        attendance.setMenstrualLeaveDays(excelVO.getMenstrualLeaveDays());
        attendance.setTotalOvertimeDuration(excelVO.getTotalOvertimeDuration());
        attendance.setWorkingDayCompensatoryLeave(excelVO.getWorkingDayCompensatoryLeave());
        attendance.setRestDayCompensatoryLeave(excelVO.getRestDayCompensatoryLeave());
        attendance.setHolidayCompensatoryLeave(excelVO.getHolidayCompensatoryLeave());
    }

}