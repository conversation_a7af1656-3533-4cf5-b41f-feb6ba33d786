package com.szcares.aps.module.app.controller.admin.account;

import com.szcares.aps.framework.common.pojo.CommonResult;
import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.framework.common.util.object.BeanUtils;
import com.szcares.aps.module.app.controller.admin.account.vo.*;
import com.szcares.aps.module.app.controller.admin.tenants.vo.TenantsRespVO;
import com.szcares.aps.module.app.convert.account.AccountConvert;
import com.szcares.aps.module.app.convert.tenants.TenantsConvert;
import com.szcares.aps.module.app.dal.dataobject.account.AccountDO;
import com.szcares.aps.module.app.service.account.AccountService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.szcares.aps.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 账户")
@RestController
@RequestMapping("/app/account")
@Validated
public class AccountController {

    @Resource
    private AccountService accountService;

    @PostMapping("create")
    @Operation(summary = "创建账户")
    public CommonResult<String> createAccount(@Valid @RequestBody AccountSaveReqVO createReqVO) {
        String accountId = accountService.createAccount(createReqVO);
        return CommonResult.success(accountId);
    }

    @PostMapping("update")
    @Operation(summary = "更新账户")
    public CommonResult<Boolean> updateAccount(@Valid @RequestBody AccountSaveReqVO updateReqVO) {
        accountService.updateAccount(updateReqVO);
        return CommonResult.success(true);
    }

    @PostMapping("updatePWD")
    @Operation(summary = "重置密码")
    public CommonResult<Boolean> updateAccountPwd(@Valid @RequestBody AccountSaveReqVO updateReqVO) {
        accountService.updateAccountPwd(updateReqVO);
        return CommonResult.success(true);
    }

    @PostMapping("delete")
    @Operation(summary = "删除账户")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteAccount(@RequestBody AccountUpdateReqVO vo) {
        accountService.deleteAccount(vo.getId());
        return CommonResult.success(true);
    }

    @GetMapping("/list")
    @Operation(summary = "获取账户列表")
    public CommonResult<PageResult<AccountRespVO>> getAccountList(AccountPageReqVO reqVO) {
        PageResult<AccountDO> list = accountService.getAccountPageList(reqVO);
        PageResult<AccountRespVO> resPage = AccountConvert.convertAccountRespVO(list);
        return success(resPage);
    }

    @GetMapping(value = {"/simple-list"})
    @Operation(summary = "获取账户精简信息列表")
    public CommonResult<List<AccountSimpleRespVO>> getSimpleAccountList(AccountListReqVO reqVO) {
        List<AccountDO> list = accountService.getAccountList(reqVO);
        List<AccountSimpleRespVO> resList = BeanUtils.toBean(list, AccountSimpleRespVO.class);
        return CommonResult.success(resList);
    }

    @GetMapping(value = {"/account-by-tenant-list"})
    @Operation(summary = "根据租户获取账户精简信息列表")
    public CommonResult<List<AccountRespVO>> getSimpleAccountListByTenant(AccountListByTenantReqVO reqVO) {
        List<AccountDO> list = accountService.getSimpleAccountListByTenant(reqVO);
        List<AccountRespVO> resPage = AccountConvert.convertList(list);
        return success(resPage);
    }

    @GetMapping("/get")
    @Operation(summary = "获得账户信息")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<AccountRespVO> getAccount(@RequestParam("id") String id) {
        AccountDO account = accountService.getAccount(id);
        return CommonResult.success(BeanUtils.toBean(account, AccountRespVO.class));
    }
}
