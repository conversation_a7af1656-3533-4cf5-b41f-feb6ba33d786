<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szcares.aps.module.app.dal.postgresql.pricing.PricingRuleMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.szcares.aps.module.app.dal.dataobject.pricing.PricingRuleDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="provider" property="provider" jdbcType="VARCHAR"/>
        <result column="model_name" property="modelName" jdbcType="VARCHAR"/>
        <result column="usage_mode" property="usageMode" jdbcType="VARCHAR"/>
        <result column="input_price" property="inputPrice" jdbcType="DECIMAL"/>
        <result column="output_price" property="outputPrice" jdbcType="DECIMAL"/>
        <result column="image_price" property="imagePrice" jdbcType="DECIMAL"/>
        <result column="markup_rate" property="markupRate" jdbcType="DECIMAL"/>
        <result column="currency" property="currency" jdbcType="VARCHAR"/>
        <result column="effective_date" property="effectiveDate" jdbcType="TIMESTAMP"/>
        <result column="expiry_date" property="expiryDate" jdbcType="TIMESTAMP"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 动态插入 -->
    <insert id="insert" parameterType="com.szcares.aps.module.app.dal.dataobject.pricing.PricingRuleDO"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO public.pricing_rules_extend
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="provider != null and provider != ''">provider,</if>
            <if test="modelName != null and modelName != ''">model_name,</if>
            <if test="usageMode != null and usageMode != ''">usage_mode,</if>
            <if test="inputPrice != null">input_price,</if>
            <if test="outputPrice != null">output_price,</if>
            <if test="imagePrice != null">image_price,</if>
            <if test="markupRate != null">markup_rate,</if>
            <if test="currency != null and currency != ''">currency,</if>
            <if test="effectiveDate != null">effective_date,</if>
            <if test="expiryDate != null">expiry_date,</if>
            <if test="createdBy != null and createdBy != ''">created_by,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
            <if test="tenantId != null and tenantId != ''">tenant_id,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="provider != null and provider != ''">#{provider},</if>
            <if test="modelName != null and modelName != ''">#{modelName},</if>
            <if test="usageMode != null and usageMode != ''">#{usageMode},</if>
            <if test="inputPrice != null">#{inputPrice},</if>
            <if test="outputPrice != null">#{outputPrice},</if>
            <if test="imagePrice != null">#{imagePrice},</if>
            <if test="markupRate != null">#{markupRate},</if>
            <if test="currency != null and currency != ''">#{currency},</if>
            <if test="effectiveDate != null">#{effectiveDate},</if>
            <if test="expiryDate != null">#{expiryDate},</if>
            <if test="createdBy != null and createdBy != ''">#{createdBy},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
            <if test="tenantId != null and tenantId != ''">#{tenantId},</if>
            <if test="status != null">#{status},</if>
        </trim>
    </insert>

</mapper>
