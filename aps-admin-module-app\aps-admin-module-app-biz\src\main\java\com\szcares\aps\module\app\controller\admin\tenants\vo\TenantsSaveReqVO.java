package com.szcares.aps.module.app.controller.admin.tenants.vo;

import com.szcares.aps.framework.common.enums.CommonStatusEnum;
import com.szcares.aps.framework.common.validation.InEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Schema(description = "管理后台 - 租户创建/修改 Request VO")
@Data
public class TenantsSaveReqVO {

    @Schema(description = "id")
    private String id;

    @Schema(description = "租户名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    private String encryptPublicKey;

    private String plan;

    private String status;

    private String customConfig;

    @Schema(description = "租户额度", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal totalQuota;

}
