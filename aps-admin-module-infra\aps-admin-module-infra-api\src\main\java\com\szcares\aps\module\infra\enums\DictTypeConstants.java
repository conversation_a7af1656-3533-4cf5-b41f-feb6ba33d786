package com.szcares.aps.module.infra.enums;

/**
 * Infra 字典类型的枚举类
 *
 * <AUTHOR>
 */
public interface DictTypeConstants {

    // 定时任务状态的枚举
    String JOB_STATUS = "infra_job_status";
    // 定时任务日志状态的枚举
    String JOB_LOG_STATUS = "infra_job_log_status";

    // API 错误日志的处理状态的枚举
    String API_ERROR_LOG_PROCESS_STATUS = "infra_api_error_log_process_status";

    // 参数配置类型
    String CONFIG_TYPE = "infra_config_type";
    // Boolean 是否类型
    String BOOLEAN_STRING = "infra_boolean_string";

    // 操作类型
    String OPERATE_TYPE = "infra_operate_type";

}
