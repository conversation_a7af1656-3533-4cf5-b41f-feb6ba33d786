package com.szcares.aps.module.agent.convert.attendance;

import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.module.agent.controller.admin.attendance.vo.*;
import com.szcares.aps.module.agent.dal.dataobject.attendance.AttendanceDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 员工考勤登记表 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface AttendanceConvert {

    AttendanceConvert INSTANCE = Mappers.getMapper(AttendanceConvert.class);

    AttendanceDO convert(AttendanceSaveReqVO bean);

    AttendanceRespVO convert(AttendanceDO bean);

    List<AttendanceRespVO> convertList(List<AttendanceDO> list);

    PageResult<AttendanceRespVO> convertPage(PageResult<AttendanceDO> page);

    List<AttendanceExportExcelVO> convertList02(List<AttendanceDO> list);

    AttendanceDO convert(AttendanceImportExcelVO bean);

}