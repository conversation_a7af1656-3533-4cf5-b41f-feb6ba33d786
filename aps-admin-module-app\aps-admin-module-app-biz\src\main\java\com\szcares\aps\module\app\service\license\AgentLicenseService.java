package com.szcares.aps.module.app.service.license;

/**
 * <AUTHOR>
 * @date 2025/6/11 11:13
 */
import com.szcares.aps.module.app.controller.admin.license.vo.AgentLicenseReq;
import com.szcares.aps.module.app.dal.dataobject.license.AgentLicenseDO;

import java.time.LocalDateTime;
import java.util.List;

public interface AgentLicenseService {
    /**
     * 生成授权码
     * @param agentName 智能体名称
     * @param agentId 智能体ID
     * @param expireTime 有效期
     * @param operator 操作人
     * @return 生成的授权码
     */
    String generateLicense(AgentLicenseReq req) throws Exception;

    /**
     * 查询授权码列表
     * @param agentId 智能体ID
     * @return 授权码列表
     */
    List<AgentLicenseDO> listLicensesByAgentId(String agentId);

    /**
     * 编辑授权码有效期（仅支持未使用且未过期）
     *
     * @param licenseId     授权码ID
     * @param newExpireTime 新有效期
     * @return 是否修改成功
     */
    int updateExpireTime(Long id, String newExpireTime);
    /**
     * 标记授权码为已使用
     * @param licenseCode 授权码
     * @param userId 使用者ID
     * @param operator 操作人
     * @return 是否标记成功
     */
    int markAsUsed(String licenseCode, String userId, String operator);
}
