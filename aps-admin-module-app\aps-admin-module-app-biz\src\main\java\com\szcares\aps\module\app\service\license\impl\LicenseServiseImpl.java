package com.szcares.aps.module.app.service.license.impl;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.module.app.controller.admin.license.vo.LicenseInfo;
import com.szcares.aps.module.app.controller.admin.license.vo.LicenseInfoVO;
import com.szcares.aps.module.app.controller.admin.license.vo.LicenseRespVO;
import com.szcares.aps.module.app.dal.dataobject.license.TenantConfigDO;
import com.szcares.aps.module.app.dal.dataobject.license.TenantConfigLogExtendDO;
import com.szcares.aps.module.app.dal.mysql.license.TenantConfigLogExtendMapper;
import com.szcares.aps.module.app.dal.mysql.license.TenantConfigMapper;
import com.szcares.aps.module.app.service.license.LicenseServise;
import com.szcares.aps.module.app.util.JwtUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2025/6/4 14:05
 */
@Service
@Slf4j
public class LicenseServiseImpl implements LicenseServise {

    @Value("${jwt.privateKey}")
    private String privateKey;
    @Value("${jwt.publicKey}")
    private String publicKey;

    private static final Pattern NESTED_OBJECT_PATTERN = Pattern.compile("([a-zA-Z_$][a-zA-Z\\d_$]*)(=)([^,=\\(]+|\\([^()]*+(?:\\([^()]*+\\)[^()]*)*+\\))");
    private static final Pattern DATE_PATTERN = Pattern.compile("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}(:\\d{2})?");

    @Resource
    TenantConfigMapper tenantConfigMapper;

    @Resource
    TenantConfigLogExtendMapper tenantConfigLogExtendMapper;

    @Override
    public int generateLicense(TenantConfigDO tenantConfigDO) throws Exception {
        // 直接传 LicenseInfo 这样的 JSON 结构对象
//        LicenseInfo licenseInfo = tenantConfigDO; // 构造或从请求中获取 LicenseInfo
        String encryptedJson = JwtUtil.encryptWithRsa(tenantConfigDO.getAuthorizeJson(), privateKey);
        log.info("授权数据加密json：" + encryptedJson);
        tenantConfigDO.setEncryptedJson(encryptedJson);
        return tenantConfigMapper.insert(tenantConfigDO);
    }


    public String getLicense(String tenantsId,String jwtToken,String clientIp) {
        // 根据租户ID和JWT Token获取租户配置信息
        TenantConfigDO tenantConfigDO = tenantConfigMapper.getLicenseByTenantId(tenantsId,jwtToken);
        // 如果租户配置信息不为空且加密JSON不为空
        if (null != tenantConfigDO && StringUtil.isNotBlank(tenantConfigDO.getEncryptedJson())) {
            //登记激活通知历史
            TenantConfigLogExtendDO logExtend = new TenantConfigLogExtendDO();
            logExtend.setTenantsId(tenantsId);
            logExtend.setIp(clientIp);
            logExtend.setJwtToken(tenantConfigDO.getEncryptedJson());
            logExtend.setCreateTime(LocalDateTime.now());
            logExtend.setUpdateTime(LocalDateTime.now());
            logExtend.setCreator(tenantsId);
            tenantConfigLogExtendMapper.insert(logExtend);
            //变更状态
            tenantConfigDO.setStatus("active");
            tenantConfigDO.setUpdateTime(new Date());
            tenantConfigMapper.updateById(tenantConfigDO);
            return tenantConfigDO.getEncryptedJson();
        } else {
            //做解密处理，如果解密成功，也新增到表里
            try{
                //jwtToken解密
                Object authorizationObject = JwtUtil.decryptWithRsa(jwtToken, publicKey);
                log.debug("解密后的授权对象: {}", authorizationObject);
                // 新增数据到tenant_config_extend表
                TenantConfigDO newTenantConfigDO = new TenantConfigDO();
                newTenantConfigDO.setTenantsId(tenantsId);
                newTenantConfigDO.setCreator(tenantsId);
                newTenantConfigDO.setUpdater(tenantsId);
                newTenantConfigDO.setEncryptedJson(jwtToken);
                if (authorizationObject instanceof Map) {
                    Map<String, Object> authMap = (Map<String, Object>) authorizationObject;
                    Map<String, Object> authMapEnterpriseInfo = (Map<String, Object>) authMap.get("enterprise_info");
                    Map<String, Object> license = (Map<String, Object>) authMapEnterpriseInfo.get("License");
                            newTenantConfigDO.setExpirationDate(LocalDateTime.parse(license.get("expiredAt").toString(), DateTimeFormatter.ISO_DATE_TIME));
                }
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode jsonNode = objectMapper.valueToTree(authorizationObject);
                newTenantConfigDO.setAuthorizeJson(jsonNode);
                newTenantConfigDO.setStatus("active");
                newTenantConfigDO.setCreateTime(new Date());
                newTenantConfigDO.setUpdateTime(new Date());
                tenantConfigMapper.insert(newTenantConfigDO);
                
                // 登记激活通知历史
                TenantConfigLogExtendDO newLogExtend = new TenantConfigLogExtendDO();
                newLogExtend.setTenantsId(tenantsId);
                newLogExtend.setIp(clientIp);
                newLogExtend.setJwtToken(jwtToken);
                newLogExtend.setCreateTime(LocalDateTime.now());
                newLogExtend.setUpdateTime(LocalDateTime.now());
                newLogExtend.setCreator(tenantsId);
                tenantConfigLogExtendMapper.insert(newLogExtend);
                
                return jwtToken;
            }catch (Exception e){
                log.error("授权码通知，报错信息："+e.getMessage());
                throw new RuntimeException("授权码有误");
            }
        }
    }

    public int updateLicense(TenantConfigDO tenantConfigDO) throws Exception {
        String  encryptedJson = JwtUtil.encryptWithRsa(tenantConfigDO.getAuthorizeJson(), privateKey);
        log.info("修改后授权数据加密json：" + encryptedJson);
        tenantConfigDO.setEncryptedJson(encryptedJson);
        int num = tenantConfigMapper.updateById(tenantConfigDO);
        return num;
    }


    @Override
    public PageResult<TenantConfigDO> getUserPage(LicenseRespVO reqVO) {
        return tenantConfigMapper.selectPage(reqVO);
    }

    @Override
    public List<TenantConfigDO> getUserList(String tenantsId) {
        return tenantConfigMapper.selectLicenseList(tenantsId);
    }

    public LicenseInfoVO decryptAuthorizationCode(String encryptedJson) throws Exception {
        // 1. 解密获取原始授权对象
        Object authorizationObject = JwtUtil.decryptWithRsa(encryptedJson, publicKey);
        log.debug("解密后的授权对象: {}", authorizationObject);

        // 2. 将 Object 转换为 JSON 字符串
        String jsonString;
        if (authorizationObject instanceof String) {
            jsonString = (String) authorizationObject;
        } else {
            ObjectMapper mapper = new ObjectMapper();
            jsonString = mapper.writeValueAsString(authorizationObject);
        }
        log.debug("转换后的JSON字符串: {}", jsonString);

        // 3. 将 JSON 字符串反序列化为 LicenseInfoVO 对象
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule()); // 注册Java时间模块
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // 新增：设置时区为UTC，匹配日期字符串中的Z（表示UTC）
        mapper.setDateFormat(new java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'"));
        mapper.setTimeZone(java.util.TimeZone.getTimeZone("UTC"));

        try {
            return mapper.readValue(jsonString, LicenseInfoVO.class);
        } catch (Exception e) {
            log.error("反序列化 LicenseInfoVO 失败: {}", e.getMessage());
            log.error("原始 JSON 字符串: {}", jsonString);
            throw new RuntimeException("授权信息解析失败", e);
        }
    }

    private String parseJavaObjectToJson(String javaObjectString) {
        // 移除所有类名前缀
        String cleaned = removeAllClassNames(javaObjectString);

        // 解析为Map
        Map<String, Object> parsedData = parseObject(cleaned);

        // 转换为JSON
        try {
            ObjectMapper mapper = new ObjectMapper();
            mapper.registerModule(new JavaTimeModule());
            return mapper.writeValueAsString(parsedData);
        } catch (Exception e) {
            log.error("转换为JSON失败: {}", e.getMessage());
            throw new RuntimeException("格式转换失败", e);
        }
    }

    private String removeAllClassNames(String input) {
        // 移除所有类名前缀，如 LicenseInfo.EnterpriseInfo( → (
        return input.replaceAll("[a-zA-Z_$][a-zA-Z\\d_$]*(\\.[a-zA-Z_$][a-zA-Z\\d_$]*)*\\(", "(");
    }

    private Map<String, Object> parseObject(String input) {
        // 移除首尾括号
        if (input.startsWith("(") && input.endsWith(")")) {
            input = input.substring(1, input.length() - 1);
        }

        Map<String, Object> result = new LinkedHashMap<>();

        // 使用正则表达式匹配所有 key=value 对，包括嵌套对象
        Matcher matcher = NESTED_OBJECT_PATTERN.matcher(input);

        while (matcher.find()) {
            String key = matcher.group(1);
            String value = matcher.group(3);

            // 处理嵌套对象
            if (value.startsWith("(") && value.endsWith(")")) {
                result.put(convertToSnakeCase(key), parseObject(value));
            }
            // 处理布尔值
            else if ("true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value)) {
                result.put(convertToSnakeCase(key), Boolean.valueOf(value));
            }
            // 处理数字
            else if (value.matches("\\d+")) {
                result.put(convertToSnakeCase(key), Integer.valueOf(value));
            }
            // 处理日期
            else if (DATE_PATTERN.matcher(value).matches()) {
                result.put(convertToSnakeCase(key), value);
            }
            // 处理字符串
            else {
                result.put(convertToSnakeCase(key), value);
            }
        }

        return result;
    }

    // 将驼峰命名转换为蛇形命名
    private String convertToSnakeCase(String camelCase) {
        return camelCase.replaceAll("([A-Z])", "_$1").toLowerCase();
    }
}