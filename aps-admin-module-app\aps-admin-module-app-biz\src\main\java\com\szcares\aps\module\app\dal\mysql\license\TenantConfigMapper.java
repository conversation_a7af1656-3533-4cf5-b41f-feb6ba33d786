package com.szcares.aps.module.app.dal.mysql.license;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.framework.mybatis.core.mapper.BaseMapperX;
import com.szcares.aps.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.szcares.aps.module.app.controller.admin.license.vo.LicenseRespVO;
import com.szcares.aps.module.app.dal.dataobject.license.TenantConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/4 16:55
 */
@Mapper
public interface TenantConfigMapper extends BaseMapperX<TenantConfigDO> {

    int generateLicense(@Param("entity") TenantConfigDO entity);
    @Update("update tenant_config set authorize_json = #{entity.authorizeJson},encrypted_json = #{entity.encryptedJson},expiration_date = #{entity.expirationDate}," +
            "status = #{entity.status},remark = #{entity.remark}, updater = #{entity.updater},update_time = #{entity.updateTime}" +
            "where tenants_id = #{entity.tenantsId}")
    int updateLicense(@Param("entity") TenantConfigDO entity);

    TenantConfigDO getLicense(@Param("tenantId") String tenantId);

    default TenantConfigDO getLicenseByTenantId(String tenantsId, String jwtToken) {
        LambdaQueryWrapperX<TenantConfigDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.select(TenantConfigDO::getEncryptedJson,TenantConfigDO::getId,TenantConfigDO::getExpirationDate,TenantConfigDO::getAuthorizeJson,TenantConfigDO::getStatus)
                .eq(TenantConfigDO::getTenantsId, tenantsId)
                .eq(StringUtils.hasText(jwtToken), TenantConfigDO::getEncryptedJson, jwtToken);

        // 执行查询并返回结果
        TenantConfigDO result = selectOne(wrapper);
        return result != null ? result : null;
    }

    default PageResult<TenantConfigDO> selectPage(LicenseRespVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TenantConfigDO>()
                // 第一个参数为 boolean 类型，控制条件是否生效
                .eq(reqVO.getTenantId() != null, TenantConfigDO::getTenantsId, reqVO.getTenantId())
                .orderByDesc(TenantConfigDO::getCreateTime));
    }

    default List<TenantConfigDO> selectLicenseList(String tenantsId) {
        LambdaQueryWrapper<TenantConfigDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(tenantsId != null, TenantConfigDO::getTenantsId, tenantsId)
                .orderByDesc(TenantConfigDO::getCreateTime);
        return selectList(wrapper);
    }
}
