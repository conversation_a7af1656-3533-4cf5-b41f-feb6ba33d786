[{"contentPath": "java/InfraStudentPageReqVO", "filePath": "aps-admin-module-infra/aps-admin-module-infra-biz/src/main/java/com.szcares.aps/module/infra/controller/admin/demo/vo/InfraStudentPageReqVO.java"}, {"contentPath": "java/InfraStudentRespVO", "filePath": "aps-admin-module-infra/aps-admin-module-infra-biz/src/main/java/com.szcares.aps/module/infra/controller/admin/demo/vo/InfraStudentRespVO.java"}, {"contentPath": "java/InfraStudentSaveReqVO", "filePath": "aps-admin-module-infra/aps-admin-module-infra-biz/src/main/java/com.szcares.aps/module/infra/controller/admin/demo/vo/InfraStudentSaveReqVO.java"}, {"contentPath": "java/InfraStudentController", "filePath": "aps-admin-module-infra/aps-admin-module-infra-biz/src/main/java/com.szcares.aps/module/infra/controller/admin/demo/InfraStudentController.java"}, {"contentPath": "java/InfraStudentDO", "filePath": "aps-admin-module-infra/aps-admin-module-infra-biz/src/main/java/com.szcares.aps/module/infra/dal/dataobject/demo/InfraStudentDO.java"}, {"contentPath": "java/InfraStudentContactDO", "filePath": "aps-admin-module-infra/aps-admin-module-infra-biz/src/main/java/com.szcares.aps/module/infra/dal/dataobject/demo/InfraStudentContactDO.java"}, {"contentPath": "java/InfraStudentTeacherDO", "filePath": "aps-admin-module-infra/aps-admin-module-infra-biz/src/main/java/com.szcares.aps/module/infra/dal/dataobject/demo/InfraStudentTeacherDO.java"}, {"contentPath": "java/InfraStudentMapper", "filePath": "aps-admin-module-infra/aps-admin-module-infra-biz/src/main/java/com.szcares.aps/module/infra/dal/mysql/demo/InfraStudentMapper.java"}, {"contentPath": "java/InfraStudentContactMapper", "filePath": "aps-admin-module-infra/aps-admin-module-infra-biz/src/main/java/com.szcares.aps/module/infra/dal/mysql/demo/InfraStudentContactMapper.java"}, {"contentPath": "java/InfraStudentTeacherMapper", "filePath": "aps-admin-module-infra/aps-admin-module-infra-biz/src/main/java/com.szcares.aps/module/infra/dal/mysql/demo/InfraStudentTeacherMapper.java"}, {"contentPath": "xml/InfraStudentMapper", "filePath": "aps-admin-module-infra/aps-admin-module-infra-biz/src/main/resources/mapper/demo/InfraStudentMapper.xml"}, {"contentPath": "java/InfraStudentServiceImpl", "filePath": "aps-admin-module-infra/aps-admin-module-infra-biz/src/main/java/com.szcares.aps/module/infra/service/demo/InfraStudentServiceImpl.java"}, {"contentPath": "java/InfraStudentService", "filePath": "aps-admin-module-infra/aps-admin-module-infra-biz/src/main/java/com.szcares.aps/module/infra/service/demo/InfraStudentService.java"}, {"contentPath": "java/InfraStudentServiceImplTest", "filePath": "aps-admin-module-infra/aps-admin-module-infra-biz/src/test/java/com.szcares.aps/module/infra/service/demo/InfraStudentServiceImplTest.java"}, {"contentPath": "java/ErrorCodeConstants_手动操作", "filePath": "aps-admin-module-infra/aps-admin-module-infra-api/src/main/java/com.szcares.aps/module/infra/enums/ErrorCodeConstants_手动操作.java"}, {"contentPath": "sql/sql", "filePath": "sql/sql.sql"}, {"contentPath": "sql/h2", "filePath": "sql/h2.sql"}, {"contentPath": "vue/index", "filePath": "aps-admin-ui-admin-vue2/src/views/infra/demo/index.vue"}, {"contentPath": "js/index", "filePath": "aps-admin-ui-admin-vue2/src/api/infra/demo/index.js"}, {"contentPath": "vue/StudentForm", "filePath": "aps-admin-ui-admin-vue2/src/views/infra/demo/StudentForm.vue"}, {"contentPath": "vue/StudentContactForm", "filePath": "aps-admin-ui-admin-vue2/src/views/infra/demo/components/StudentContactForm.vue"}, {"contentPath": "vue/StudentTeacherForm", "filePath": "aps-admin-ui-admin-vue2/src/views/infra/demo/components/StudentTeacherForm.vue"}, {"contentPath": "vue/StudentContactList", "filePath": "aps-admin-ui-admin-vue2/src/views/infra/demo/components/StudentContactList.vue"}, {"contentPath": "vue/StudentTeacherList", "filePath": "aps-admin-ui-admin-vue2/src/views/infra/demo/components/StudentTeacherList.vue"}]