package com.szcares.aps.module.app.controller.admin.report.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "管理后台 - 获取AI图片配额排名数据 Response VO")
public class GetAiImageQuotaRankingRes {

    private int ranking;         // 排名
    private String address;      // 域名
    private String path;         // 路径
    private double totalCost;    // 总花费
    private int recordNum;       // 调用次数
    private String model;        // 模型
}
