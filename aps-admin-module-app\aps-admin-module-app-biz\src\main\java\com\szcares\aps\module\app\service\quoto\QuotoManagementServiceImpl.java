package com.szcares.aps.module.app.service.quoto;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.annotations.VisibleForTesting;
import com.szcares.aps.module.app.controller.admin.quoto.vo.GetAccountQuotaRankingDataReq;
import com.szcares.aps.module.app.controller.admin.quoto.vo.GetQuotaManagementDataResponse;
import com.szcares.aps.module.app.controller.admin.quoto.vo.PageResult;
import com.szcares.aps.module.app.controller.admin.quoto.vo.SetUserQuotaReq;
import com.szcares.aps.module.app.dal.dataobject.quoto.QuotoDO;
import com.szcares.aps.module.app.dal.postgresql.quoto.QuotaManagementMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.szcares.aps.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.szcares.aps.module.system.enums.ErrorCodeConstants.*;

import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Service
public class QuotoManagementServiceImpl implements QuotoManagementService{

    @Resource
    private QuotaManagementMapper quotaManagementMapper;

    @Override
    @DS("apsds")
    public void setUserQuota(SetUserQuotaReq req) {
        //UUID uuid = UUID.fromString(req.getId());
        // 校验自己存在
        validateQuotoExists(req.getId());
        // 构建更新对象
        QuotoDO quotoDO = new QuotoDO();
        quotoDO.setAccountId(req.getId());
        quotoDO.setTotalQuota(req.getQuota());
        //更新时间
        quotoDO.setUpdatedAt(LocalDateTime.now());
        //更新
        try {
            quotaManagementMapper.customUpdateById(quotoDO);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    @DS("apsds")
    public  PageResult<GetQuotaManagementDataResponse> getQuotaManagementList(GetAccountQuotaRankingDataReq req){
        // 1.校验参数
        validateRequest(req);
        // 处理分页参数
        int pageSize = Optional.ofNullable(req.getPageSize()).orElse(10);
        int page = req.getPage() != null ? req.getPage() : 1;
        int offset = (page - 1) * pageSize;

        // 2. 查询数据与总数
        List<GetQuotaManagementDataResponse> responses = quotaManagementMapper.findResult(req, offset);
        long total = quotaManagementMapper.countResult(req);
        return new PageResult<>(responses, total, page, pageSize);
    }

    @VisibleForTesting
    @DS("apsds")
    public void validateQuotoExists(String uuid){
        if (uuid == null){
            return;
        }
        UUID uid = UUID.fromString(uuid);
        QuotoDO quotoDO = quotaManagementMapper.selectById(uuid);
        if (quotoDO == null){
            throw exception(QUOTA_NOT_EXISTS);
        }
    }

    @VisibleForTesting
    private void validateRequest(GetAccountQuotaRankingDataReq req) {
        if (req == null) {
            throw exception(QUOTA_PARAM_NOT_NULL);
        }

        if (req.getPage() != null && req.getPage() < 1) {
            throw exception(QUOTA_PAGE_NUM_GT_0);
        }

        if (req.getPageSize() != null && req.getPageSize() < 1) {
            throw exception(QUOTA_PAGE_SIZE_GT_0);
        }
    }

}
