package com.szcares.aps.module.app.dal.dataobject.license;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.time.LocalDateTime;
import com.fasterxml.jackson.databind.JsonNode;
/**
 * 租户授权码配置表
 */
@Data
@TableName("tenant_config_extend")
public class TenantConfigDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 租户授权码主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 部署租户的id
     */
    @TableField("tenants_id")
    private String tenantsId;

    /**
     * 授权数据json
     */
    @TableField(value = "authorize_json", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    private JsonNode authorizeJson;

    /**
     * 授权数据加密json
     */
    private String encryptedJson;

    /**
     * 部署用户名
     */
    private String userName;

    /**
     * 有效期
     */
    private LocalDateTime expirationDate;

    /**
     * 授权码使用状态，active：已使用；expired：过期
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    private String updater;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableLogic
    private Boolean deleted;
}
