package com.szcares.aps.module.app.controller.admin.license;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.szcares.aps.framework.common.pojo.CommonResult;
import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.framework.common.util.object.BeanUtils;
import com.szcares.aps.framework.security.core.LoginUser;
import com.szcares.aps.framework.security.core.util.SecurityFrameworkUtils;
import com.szcares.aps.module.app.controller.admin.license.vo.*;
import com.szcares.aps.module.app.dal.dataobject.license.TenantConfigDO;
import com.szcares.aps.module.app.dal.dataobject.license.TenantConfigLogExtendDO;
import com.szcares.aps.module.app.service.license.LicenseServise;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.szcares.aps.framework.common.pojo.CommonResult.success;
import static com.szcares.aps.framework.common.util.collection.CollectionUtils.convertList;

/**
 * <AUTHOR>
 * @date 2025/6/3 16:29
 */
@Tag(name = "管理后台 - 授权码管理")
@RestController
@RequestMapping("/app/license")
@Validated
@Slf4j
public class LicenseController {

    @Resource
    private LicenseServise licenseService;

    @Resource
    private ObjectMapper objectMapper; // 注入 ObjectMapper

    @PostMapping("/generateLicense")
    @Operation(summary = "生成授权码")
    public CommonResult<String> generateLicense(@Valid @RequestBody LicenseInfo req) throws Exception {

        try {
            String expiredAt = req.getEnterpriseInfo().getLicense().getExpiredAt();
            DateTimeFormatter formatter = DateTimeFormatter.ISO_DATE_TIME;
            LocalDateTime expiredDateTime = LocalDateTime.parse(expiredAt, formatter);
            //获取登录用户信息

            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
            Map<String, String> userInfo = loginUser.getInfo();
            //登记入库
            TenantConfigDO tenantConfigDO = new TenantConfigDO();
            tenantConfigDO.setExpirationDate(expiredDateTime);
            tenantConfigDO.setTenantsId(req.getTenantId());
            // 使用注入的 ObjectMapper 将对象转换为 JsonNode
            tenantConfigDO.setAuthorizeJson(objectMapper.valueToTree(req));
            tenantConfigDO.setCreator(null != userInfo ?userInfo.get("nickname"):loginUser.getId().toString());
            tenantConfigDO.setCreateTime(new Date());
            tenantConfigDO.setUpdater(null != userInfo ?userInfo.get("nickname"):loginUser.getId().toString());
            tenantConfigDO.setUpdateTime(new Date());
            tenantConfigDO.setStatus(req.getEnterpriseInfo().getLicense().getStatus());
            int num = licenseService.generateLicense(tenantConfigDO);
            if(num ==1){
                return CommonResult.success("成功生成授权码");
            }else {
                return CommonResult.success("生成授权码失败");
            }

        } catch (Exception e){
            log.error("授权码生成异常:",e);
            return CommonResult.error(999,"授权码生成异常");
        }

    }

    @PostMapping("/notify")
    @Operation(summary = "通知启用授权码")
    public CommonResult<String> getLicense(@RequestBody LicenseReqVO respVO) throws Exception {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        // 获取请求IP地址
        String clientIp = getClientIpAddress(request);
        log.info("客户端IP地址: {}", clientIp);
        String jwtToken = licenseService.getLicense(respVO.getTenantId(),respVO.getJwtToken(),clientIp);
        if (StringUtils.isBlank(jwtToken)){
            return CommonResult.error(999,"授权码不存在");
        }
        return CommonResult.success(jwtToken);
    }

    /**
     * 获取客户端真实IP地址（处理代理情况）
     */
    private String getClientIpAddress(HttpServletRequest request) {
        // 优先从HTTP头中获取客户端IP（适用于经过代理的情况）
        String ip = request.getHeader("X-Real-IP");
        if (isUnknown(ip)) {
            ip = request.getHeader("X-Forwarded-For");
        }
        if (isUnknown(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (isUnknown(ip)) {
            ip = request.getHeader("X-Proxy-Client-IP");
        }
        if (isUnknown(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (isUnknown(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        // 最后尝试从远程地址获取（适用于没有代理的情况）
        if (isUnknown(ip)) {
            ip = request.getRemoteAddr();
            // 处理IPv6回环地址
            if ("0:0:0:0:0:0:0:1".equals(ip)) {
                ip = "127.0.0.1";
            }
        }
        // 处理多级代理的情况，取第一个非unknown的IP
        if (ip != null && ip.contains(",")) {
            String[] ips = ip.split(",");
            for (String i : ips) {
                if (!isUnknown(i)) {
                    ip = i;
                    break;
                }
            }
        }
        return ip;
    }

    /**
     * 判断IP是否为未知或无效
     */
    private boolean isUnknown(String ip) {
        return ip == null || ip.trim().length() == 0 || "unknown".equalsIgnoreCase(ip);
    }

    @PostMapping("/updateLicense")
    @Operation(summary = "修改授权码")
    public CommonResult<String> updateLicense(@Valid @RequestBody LicenseReq req) throws Exception {
        try {
            String expiredAt = req.getEnterpriseInfo().getLicense().getExpiredAt();
            DateTimeFormatter formatter = DateTimeFormatter.ISO_DATE_TIME;
            LocalDateTime expiredDateTime = LocalDateTime.parse(expiredAt, formatter);
            //获取登录用户信息
            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
            Map<String, String> userInfo = loginUser.getInfo();
            //登记入库
            TenantConfigDO tenantConfigDO = new TenantConfigDO();
            tenantConfigDO.setExpirationDate(expiredDateTime);
            tenantConfigDO.setTenantsId(req.getTenantId());
            // 使用注入的 ObjectMapper 将对象转换为 JsonNode
            tenantConfigDO.setAuthorizeJson(objectMapper.valueToTree(req));
            tenantConfigDO.setCreator(null != userInfo ?userInfo.get("nickname"):loginUser.getId().toString());
            tenantConfigDO.setCreateTime(new Date());
            tenantConfigDO.setUpdater(null != userInfo ?userInfo.get("nickname"):loginUser.getId().toString());
            tenantConfigDO.setUpdateTime(new Date());
            tenantConfigDO.setStatus(req.getEnterpriseInfo().getLicense().getStatus());
            tenantConfigDO.setId(req.getId());
            int num = licenseService.updateLicense(tenantConfigDO);
            if(num ==1){
                return CommonResult.success("成功修改授权码");
            }else {
                return CommonResult.success("修改授权码失败");
            }

        } catch (Exception e){
            log.error("授权码修改异常:",e);
            return CommonResult.error(999,"授权码修改异常");
        }

    }

    @PostMapping("/decryptAuthorizationCode")
    @Operation(summary = "解密授权码信息")
    public CommonResult<LicenseInfoVO> decrypt(@RequestParam("encryptedJson") String encryptedJson) throws Exception {

        try {
            LicenseInfoVO vo = licenseService.decryptAuthorizationCode(encryptedJson);

            return CommonResult.success(vo);
        }catch (Exception e){
            log.error("解密授权码异常:",e);
            return CommonResult.error(999,"解密授权码异常");
        }
    }

    @GetMapping("/page")
    @Operation(summary = "获得租户授权码分页列表")
    public CommonResult<PageResult<TenantConfigVO>> getLicensePage(@Valid LicenseRespVO pageReqVO) {
        // 获得用户分页列表
        PageResult<TenantConfigDO> pageResult = licenseService.getUserPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TenantConfigVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得租户授权码列表")
    public CommonResult<List<TenantConfigVO>> getLicenseList(@RequestParam("tenantsId") String tenantsId) {
        // 获得用户分页列表
        List<TenantConfigDO> list = licenseService.getUserList(tenantsId);
        return success(BeanUtils.toBean(list, TenantConfigVO.class));
    }
}