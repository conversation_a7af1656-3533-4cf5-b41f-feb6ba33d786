package com.szcares.aps.module.agent.controller.admin.entryexit;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import com.szcares.aps.framework.common.pojo.CommonResult;
import com.szcares.aps.framework.common.pojo.PageParam;
import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.framework.excel.core.util.ExcelUtils;
import com.szcares.aps.module.agent.controller.admin.entryexit.vo.*;
import com.szcares.aps.module.agent.convert.entryexit.EntryExitConvert;
import com.szcares.aps.module.agent.dal.dataobject.entryexit.EntryExitDO;
import com.szcares.aps.module.agent.service.entryexit.EntryExitService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

import static com.szcares.aps.framework.common.pojo.CommonResult.success;
import static com.szcares.aps.framework.common.pojo.CommonResult.error;

@Tag(name = "管理后台 - 员工入职离职登记表")
@RestController
@RequestMapping("/agent/entry-exit")
@Validated
@Slf4j
public class EntryExitController {

    @Resource
    private EntryExitService entryExitService;

    @PostMapping("/create")
    @Operation(summary = "创建员工入职离职登记表")
//    @PreAuthorize("@ss.hasPermission('agent:entry-exit:create')")
    public CommonResult<Long> createEntryExit(@Valid @RequestBody EntryExitSaveReqVO createReqVO) {
        return success(entryExitService.createEntryExit(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新员工入职离职登记表")
//    @PreAuthorize("@ss.hasPermission('agent:entry-exit:update')")
    public CommonResult<Boolean> updateEntryExit(@Valid @RequestBody EntryExitSaveReqVO updateReqVO) {
        entryExitService.updateEntryExit(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除员工入职离职登记表")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('agent:entry-exit:delete')")
    public CommonResult<Boolean> deleteEntryExit(@RequestParam("id") Long id) {
        entryExitService.deleteEntryExit(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得员工入职离职登记表")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('agent:entry-exit:query')")
    public CommonResult<EntryExitRespVO> getEntryExit(@RequestParam("id") Long id) {
        EntryExitDO entryExit = entryExitService.getEntryExit(id);
        return success(EntryExitConvert.INSTANCE.convert(entryExit));
    }

    @GetMapping("/page")
    @Operation(summary = "获得员工入职离职登记表分页")
//    @PreAuthorize("@ss.hasPermission('agent:entry-exit:query')")
    public CommonResult<PageResult<EntryExitRespVO>> getEntryExitPage(@Valid EntryExitPageReqVO pageVO) {
        PageResult<EntryExitRespVO> pageResult = entryExitService.getEntryExitPage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出员工入职离职登记表 Excel")
//    @PreAuthorize("@ss.hasPermission('agent:entry-exit:export')")
//    @OperateLog(type = EXPORT)
    public void exportEntryExitExcel(@Valid EntryExitPageReqVO pageVO,
              HttpServletResponse response) throws IOException {
        pageVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<EntryExitExportExcelVO> list = entryExitService.getEntryExitExportList(pageVO);
        // 导出 Excel
        ExcelUtils.write(response, "员工入职离职登记表.xls", "数据", EntryExitExportExcelVO.class, list);
    }

//    @GetMapping("/get-import-template")
//    @Operation(summary = "获得员工入职离职登记表导入模板")
//    public void importTemplate(HttpServletResponse response) throws IOException {
//        // 手动创建导出 demo
//        List<EntryExitImportExcelVO> list = Arrays.asList(
//                new EntryExitImportExcelVO().setEmployeeName("张三").setMovementType("入职").setMovementDate("2024-01-01"),
//                new EntryExitImportExcelVO().setEmployeeName("李四").setMovementType("离职").setMovementDate("2024-12-31")
//        );
//        ExcelUtils.write(response, "员工入职离职登记表导入模板.xls", "员工入职离职登记表", EntryExitImportExcelVO.class, list);
//    }

    @PostMapping("/import")
    @Operation(summary = "导入员工入职离职登记表 Excel")
//    @PreAuthorize("@ss.hasPermission('agent:entry-exit:import')")
//    @OperateLog(type = IMPORT)
    public CommonResult<EntryExitImportRespVO> importEntryExitExcel(@RequestParam("file") MultipartFile file) throws Exception {
        try {
            log.info("开始导入员工入职离职Excel文件，文件名: {}", file.getOriginalFilename());

            // 存储所有sheet的数据
            List<EntryExitImportExcelVO> allData = new ArrayList<>();

            // 定义要处理的sheet信息（根据你的Excel结构）
            String[] sheetNames = {"2025入职", "2025离职"}; // 可以根据实际情况调整

            // 遍历每个预定义的sheet
            for (int sheetIndex = 0; sheetIndex < sheetNames.length; sheetIndex++) {
                String expectedSheetName = sheetNames[sheetIndex];
                log.info("正在处理sheet: {}", expectedSheetName);

                // 解析sheet名称，确定movementType和year
                String movementType = null;
                Integer year = null;

                if (expectedSheetName.contains("入职")) {
                    movementType = "入职";
                } else if (expectedSheetName.contains("离职")) {
                    movementType = "离职";
                }

                // 从sheet名称中提取年份（如：2025入职、2025离职）
                try {
                    String yearStr = expectedSheetName.replaceAll("[^0-9]", "");
                    if (!yearStr.isEmpty()) {
                        year = Integer.parseInt(yearStr);
                    }
                } catch (NumberFormatException e) {
                    log.warn("无法从sheet名称 '{}' 中解析年份", expectedSheetName);
                }

                if (movementType == null) {
                    log.warn("无法从sheet名称 '{}' 中确定变动类型，跳过该sheet", expectedSheetName);
                    continue;
                }

                log.info("Sheet '{}' 解析结果: movementType={}, year={}", expectedSheetName, movementType, year);

                try {
                    // 读取当前sheet的数据
                    List<EntryExitImportExcelVO> sheetData = EasyExcel.read(file.getInputStream(), EntryExitImportExcelVO.class, null)
                            .excelType(ExcelTypeEnum.XLSX)
                            .sheet(sheetIndex) // 使用sheet索引
                            .headRowNumber(1) // 第一行是标题
                            .autoTrim(true)
                            .doReadSync();

                    log.info("从sheet '{}' 读取到{}条数据", expectedSheetName, sheetData.size());

                    // 为每条数据设置movementType和year，并过滤有效数据
                    final String finalMovementType = movementType;
                    final Integer finalYear = year;

                    List<EntryExitImportExcelVO> validData = sheetData.stream()
                            .filter(data -> {
                                // 只有姓名和日期不为空才登记数据
                                boolean isValid = data.getEmployeeName() != null && !data.getEmployeeName().trim().isEmpty()
                                        && data.getMovementDate() != null && !data.getMovementDate().trim().isEmpty();

                                if (isValid) {
                                    data.setMovementType(finalMovementType);
                                    data.setYear(finalYear);
                                    log.debug("有效数据: 姓名={}, 日期={}, 类型={}, 年份={}",
                                            data.getEmployeeName(), data.getMovementDate(), finalMovementType, finalYear);
                                } else {
                                    log.debug("跳过无效数据: 姓名={}, 日期={}", data.getEmployeeName(), data.getMovementDate());
                                }

                                return isValid;
                            })
                            .collect(Collectors.toList());

                    log.info("Sheet '{}' 过滤后有效数据{}条", expectedSheetName, validData.size());
                    allData.addAll(validData);

                } catch (Exception e) {
                    log.warn("读取sheet '{}' 失败: {}", expectedSheetName, e.getMessage());
                    // 继续处理下一个sheet
                }
            }

            log.info("所有sheet处理完成，总共有效数据{}条", allData.size());

            if (allData.isEmpty()) {
                return error(400, "Excel文件中没有读取到有效数据，请检查文件格式和内容");
            }

            // 调用service处理数据
            EntryExitImportRespVO result = entryExitService.importEntryExitList(allData);
            log.info("数据导入完成，创建{}条，更新{}条，失败{}条",
                    result.getCreateNames().size(),
                    result.getUpdateNames().size(),
                    result.getFailureNames().size());

            return success(result);

        } catch (Exception e) {
            log.error("Excel导入失败", e);
            return error(500, "Excel导入失败: " + e.getMessage());
        }
    }

}