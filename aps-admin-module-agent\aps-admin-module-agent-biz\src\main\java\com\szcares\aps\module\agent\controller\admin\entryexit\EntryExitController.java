package com.szcares.aps.module.agent.controller.admin.entryexit;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.szcares.aps.framework.common.pojo.CommonResult;
import com.szcares.aps.framework.common.pojo.PageParam;
import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.framework.excel.core.util.ExcelUtils;
import com.szcares.aps.module.agent.controller.admin.entryexit.vo.*;
import com.szcares.aps.module.agent.convert.entryexit.EntryExitConvert;
import com.szcares.aps.module.agent.dal.dataobject.entryexit.EntryExitDO;
import com.szcares.aps.module.agent.service.entryexit.EntryExitService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

import static com.szcares.aps.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 员工入职离职登记表")
@RestController
@RequestMapping("/agent/entry-exit")
@Validated
@Slf4j
public class EntryExitController {

    @Resource
    private EntryExitService entryExitService;

    @PostMapping("/create")
    @Operation(summary = "创建员工入职离职登记表")
//    @PreAuthorize("@ss.hasPermission('agent:entry-exit:create')")
    public CommonResult<Long> createEntryExit(@Valid @RequestBody EntryExitSaveReqVO createReqVO) {
        return success(entryExitService.createEntryExit(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新员工入职离职登记表")
//    @PreAuthorize("@ss.hasPermission('agent:entry-exit:update')")
    public CommonResult<Boolean> updateEntryExit(@Valid @RequestBody EntryExitSaveReqVO updateReqVO) {
        entryExitService.updateEntryExit(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除员工入职离职登记表")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('agent:entry-exit:delete')")
    public CommonResult<Boolean> deleteEntryExit(@RequestParam("id") Long id) {
        entryExitService.deleteEntryExit(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得员工入职离职登记表")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('agent:entry-exit:query')")
    public CommonResult<EntryExitRespVO> getEntryExit(@RequestParam("id") Long id) {
        EntryExitDO entryExit = entryExitService.getEntryExit(id);
        return success(EntryExitConvert.INSTANCE.convert(entryExit));
    }

    @GetMapping("/page")
    @Operation(summary = "获得员工入职离职登记表分页")
//    @PreAuthorize("@ss.hasPermission('agent:entry-exit:query')")
    public CommonResult<PageResult<EntryExitRespVO>> getEntryExitPage(@Valid EntryExitPageReqVO pageVO) {
        PageResult<EntryExitRespVO> pageResult = entryExitService.getEntryExitPage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出员工入职离职登记表 Excel")
//    @PreAuthorize("@ss.hasPermission('agent:entry-exit:export')")
//    @OperateLog(type = EXPORT)
    public void exportEntryExitExcel(@Valid EntryExitPageReqVO pageVO,
              HttpServletResponse response) throws IOException {
        pageVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<EntryExitExportExcelVO> list = entryExitService.getEntryExitExportList(pageVO);
        // 导出 Excel
        ExcelUtils.write(response, "员工入职离职登记表.xls", "数据", EntryExitExportExcelVO.class, list);
    }

//    @GetMapping("/get-import-template")
//    @Operation(summary = "获得员工入职离职登记表导入模板")
//    public void importTemplate(HttpServletResponse response) throws IOException {
//        // 手动创建导出 demo
//        List<EntryExitImportExcelVO> list = Arrays.asList(
//                new EntryExitImportExcelVO().setEmployeeName("张三").setMovementType("入职").setMovementDate("2024-01-01"),
//                new EntryExitImportExcelVO().setEmployeeName("李四").setMovementType("离职").setMovementDate("2024-12-31")
//        );
//        ExcelUtils.write(response, "员工入职离职登记表导入模板.xls", "员工入职离职登记表", EntryExitImportExcelVO.class, list);
//    }

    @PostMapping("/import")
    @Operation(summary = "导入员工入职离职登记表 Excel")
//    @PreAuthorize("@ss.hasPermission('agent:entry-exit:import')")
//    @OperateLog(type = IMPORT)
    public CommonResult<EntryExitImportRespVO> importEntryExitExcel(@RequestParam("file") MultipartFile file) throws Exception {
        try {
            List<EntryExitImportExcelVO> list = EasyExcel.read(file.getInputStream(), EntryExitImportExcelVO.class, null)
                    .excelType(ExcelTypeEnum.XLSX) //可指定文件的导入类型
                    .sheet(0)//读取sheet页码，读取多个sheet页可利用循环来进行读取，页码从0开始
                    .head(EntryExitImportExcelVO.class) // 指定实体类
                    .autoTrim(false) //读取数据时忽略空格，默认忽略
                    .doReadSync();
            //读取到的数据
            return success(entryExitService.importEntryExitList(list));
        } catch (Exception e) {
            return null;
        }

    }

}