package com.szcares.aps.module.app.controller.admin.license;

/**
 * <AUTHOR>
 * @date 2025/6/11 11:28
 */
import com.szcares.aps.framework.common.pojo.CommonResult;
import com.szcares.aps.framework.common.util.object.BeanUtils;
import com.szcares.aps.module.app.controller.admin.license.vo.AgentLicenseReq;
import com.szcares.aps.module.app.controller.admin.license.vo.TenantConfigVO;
import com.szcares.aps.module.app.dal.dataobject.license.AgentLicenseDO;
import com.szcares.aps.module.app.service.license.AgentLicenseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static com.szcares.aps.framework.common.pojo.CommonResult.success;

@RestController
@RequestMapping("/api/agentLicense")
@Tag(name = "智能体授权管理", description = "智能体授权码增删改查接口")
public class AgentLicenseController {

    @Resource
    AgentLicenseService licenseService;


    @PostMapping("/generate")
    @Operation(summary = "生成授权码")
    public CommonResult<String> generateLicense(@RequestBody AgentLicenseReq req) throws Exception {
        String code =  licenseService.generateLicense(req);
        if (StringUtils.hasText(code)){
            return CommonResult.success("成功生成授权码");
        }
        return CommonResult.success("生成授权码失败");
    }

    @GetMapping("/list")
    @Parameter(name = "agentId", description = "智能体id", required = false)
    @Operation(summary = "查询授权码列表")
    public CommonResult<List<AgentLicenseDO>> listLicenses(@RequestParam(required = false) String agentId) {
        List<AgentLicenseDO> list = licenseService.listLicensesByAgentId(agentId);
        return success(BeanUtils.toBean(list, AgentLicenseDO.class));
    }

    @PostMapping("/update")
    @Operation(summary = "修改授权码有效期（仅未使用且未过期）")
    @Parameter(name = "id", description = "编号", required = true)
    @Parameter(name = "newExpireTime", description = "新的有效期", required = true)
    public CommonResult<String> updateExpireTime(
            @RequestParam Long id,
            @RequestParam String newExpireTime) {
        int num = licenseService.updateExpireTime(id, newExpireTime);
        if(num ==1){
            return CommonResult.success("成功修改授权码");
        }else {
            return CommonResult.success("修改授权码失败");
        }
    }

    @PostMapping("/markUsed")
    @Operation(summary = "标记授权码为已使用")
    @Parameter(name = "licenseCode", description = "智能体授权码", required = true)
    @Parameter(name = "userId", description = "使用者id")
    @Parameter(name = "operator", description = "操作人")
    public CommonResult<String> markAsUsed(
            @RequestParam String licenseCode,
            @RequestParam String userId,
            @RequestParam String operator) {
        int num =  licenseService.markAsUsed(licenseCode, userId, operator);
        if(num ==1){
            return CommonResult.success("成功标记授权码为已使用");
        }else {
            return CommonResult.success("标记授权码为已使用失败");
        }
    }
}
