package com.szcares.aps.module.app.controller.admin.license.vo;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 许可证信息
 */
@Data
@Schema(description = "许可证信息")
public class LicenseInfo {

    @Schema(description = "租户id")
    private String tenantId;

    @JsonProperty("enterprise_info")
    @Schema(description = "企业信息")
    private EnterpriseInfo enterpriseInfo;

    @JsonProperty("billing_info")
    @Schema(description = "计费信息")
    private BillingInfo billingInfo;

    @Data
    @Schema(description = "企业信息")
    public static class EnterpriseInfo {

        @JsonProperty("SSOEnforcedForSignin")
        @Schema(description = "是否强制使用SSO进行登录")
        private boolean ssoEnforcedForSignin;

        @JsonProperty("SSOEnforcedForSigninProtocol")
        @Schema(description = "强制使用SSO登录的协议类型")
        private String ssoEnforcedForSigninProtocol;

        @Schema(description = "是否强制使用SSO进行Web访问")
        private boolean ssoEnforcedForWeb;

        @JsonAlias("sso_enforced_for_web_protocol")
        @JsonProperty("SSOEnforcedForWebProtocol")
        @Schema(description = "强制使用SSO进行Web访问的协议类型")
        private String ssoEnforcedForWebProtocol;

        @JsonProperty("EnableEmailCodeLogin")
        @Schema(description = "是否启用邮箱验证码登录")
        private boolean enableEmailCodeLogin;

        @JsonProperty("EnableEmailPasswordLogin")
        @Schema(description = "是否启用邮箱密码登录")
        private boolean enableEmailPasswordLogin;

        @JsonProperty("IsAllowRegister")
        @Schema(description = "是否允许用户注册")
        private boolean isAllowRegister;

        @JsonProperty("IsAllowCreateWorkspace")
        @Schema(description = "是否允许创建工作空间")
        private boolean isAllowCreateWorkspace;

        @JsonProperty("License")
        @Schema(description = "许可证信息")
        private License license;

        @JsonProperty("Branding")
        @Schema(description = "品牌信息")
        private Branding branding;

        @JsonProperty("WebAppAuth")
        @Schema(description = "Web应用认证配置")
        private WebAppAuth webAppAuth;
    }

    @Data
    @Schema(description = "许可证详情")
    public static class License {

        @Schema(description = "许可证状态")
        private String status;

        @Schema(description = "许可证过期时间")
        private String expiredAt;

        @Schema(description = "工作空间配额")
        private Workspaces workspaces;
    }

    @Data
    @Schema(description = "工作空间配额")
    public static class Workspaces {
        @Schema(description = "是否启用工作空间")
        private boolean enabled;
        @Schema(description = "工作空间数量限制")
        private Integer limit;
        @Schema(description = "已使用的工作空间数量")
        private Integer used;
    }

    @Data
    @Schema(description = "品牌信息")
    public static class Branding {

        @Schema(description = "应用标题")
        private String applicationTitle;

        @Schema(description = "登录页Logo")
        private String loginPageLogo;

        @Schema(description = "工作空间Logo")
        private String workspaceLogo;

        @Schema(description = "网站图标")
        private String favicon;
    }

    @Data
    @Schema(description = "Web应用认证配置")
    public static class WebAppAuth {
        @Schema(description = "是否允许SSO登录")
        private boolean allowSso;

        @Schema(description = "是否允许邮箱验证码登录")
        private boolean allowEmailCodeLogin;

        @Schema(description = "是否允许邮箱密码登录")
        private boolean allowEmailPasswordLogin;
    }

    @Data
    @Schema(description = "计费信息")
    public static class BillingInfo {

        @Schema(description = "是否启用计费功能")
        private boolean enabled;

        @Schema(description = "订阅信息")
        private Subscription subscription;

        @Schema(description = "成员配额")
        private Quota members;

        @Schema(description = "应用配额")
        private Quota apps;

        @JsonProperty("vector_space")
        @Schema(description = "向量空间配额")
        private Quota vectorSpace;

        @JsonProperty("documents_upload_quota")
        @Schema(description = "文档上传配额")
        private Quota documentsUploadQuota;

        @JsonProperty("annotation_quota_limit")
        @Schema(description = "标注配额限制")
        private Quota annotationQuotaLimit;

        @JsonProperty("docs_processing")
        @Schema(description = "是否启用文档处理功能")
        private boolean docsProcessing;

        @JsonProperty("can_replace_logo")
        @Schema(description = "是否允许替换Logo")
        private boolean canReplaceLogo;

        @JsonProperty("model_load_balancing_enabled")
        @Schema(description = "是否启用模型负载均衡")
        private boolean modelLoadBalancingEnabled;

        @JsonProperty("knowledge_rate_limit")
        @Schema(description = "知识请求速率限制")
        private RateLimit knowledgeRateLimit;
    }

    @Data
    @Schema(description = "订阅信息")
    public static class Subscription {
        @Schema(description = "订阅计划")
        private String plan;
        @Schema(description = "订阅周期")
        private String interval;
    }

    @Data
    @Schema(description = "配额信息")
    public static class Quota {
        @Schema(description = "已使用大小")
        private Integer size;
        @Schema(description = "限制大小")
        private Integer limit;
    }

    @Data
    @Schema(description = "速率限制")
    public static class RateLimit {
        @Schema(description = "速率限制值")
        private Integer limit;
    }
}