package com.szcares.aps.module.app.controller.admin.pricing;

import com.szcares.aps.framework.common.pojo.CommonResult;
import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.framework.common.util.object.BeanUtils;
import com.szcares.aps.module.app.controller.admin.pricing.vo.*;
import com.szcares.aps.module.app.convert.pricing.PricingRuleConvert;
import com.szcares.aps.module.app.dal.dataobject.pricing.PricingRuleDO;
import com.szcares.aps.module.app.service.plugin.impl.PluginDeclarationServiceImpl;
import com.szcares.aps.module.app.service.pricing.PricingRuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.szcares.aps.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 定价规则")
@RestController
@RequestMapping("/app/pricing-rule")
@Validated
public class PricingRuleController {

    @Resource
    private PricingRuleService pricingRuleService;

    @Resource
    private PluginDeclarationServiceImpl pluginDeclarationService;

    @GetMapping("/llmList")
    @Operation(summary = "获取模型列表")
    public CommonResult<List<PluginModelVO>> list() {
        return CommonResult.success(pluginDeclarationService.getPluginModelList());
    }

    @PostMapping("create")
    @Operation(summary = "创建定价规则")
    public CommonResult<Long> createPricingRule(@Valid @RequestBody PricingRuleSaveReqVO createReqVO) {
        Long id = pricingRuleService.createPricingRule(createReqVO);
        return success(id);
    }

    @PostMapping("update")
    @Operation(summary = "更新定价规则")
    public CommonResult<Boolean> updatePricingRule(@Valid @RequestBody PricingRuleUpdateReqVO updateReqVO) {
        pricingRuleService.updatePricingRule(updateReqVO);
        return success(true);
    }

    @PostMapping("delete")
    @Operation(summary = "删除定价规则")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deletePricingRule(@RequestBody PricingRuleUpdateReqVO vo) {
        pricingRuleService.deletePricingRule(vo.getId());
        return success(true);
    }

    @GetMapping("/list")
    @Operation(summary = "获取定价规则分页列表")
    public CommonResult<PageResult<PricingRuleRespVO>> getPricingRuleList(PricingRulePageReqVO reqVO) {
        PageResult<PricingRuleDO> list = pricingRuleService.getPricingRulePageList(reqVO);
        PageResult<PricingRuleRespVO> resPage = PricingRuleConvert.convertPricingRuleRespVO(list);
        return success(resPage);
    }

    @GetMapping("/get")
    @Operation(summary = "获得定价规则信息")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<PricingRuleRespVO> getPricingRule(@RequestParam("id") Long id) {
        PricingRuleDO rule = pricingRuleService.getPricingRule(id);
        return success(BeanUtils.toBean(rule, PricingRuleRespVO.class));
    }
}