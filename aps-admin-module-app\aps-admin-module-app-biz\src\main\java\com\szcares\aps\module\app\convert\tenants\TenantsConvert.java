package com.szcares.aps.module.app.convert.tenants;

import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.module.app.controller.admin.tenants.TenantsController;
import com.szcares.aps.module.app.controller.admin.tenants.vo.TenantsRespVO;
import com.szcares.aps.module.app.dal.dataobject.tenants.TenantsDO;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class TenantsConvert {

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static PageResult<TenantsRespVO> convertTenantsRespVO(PageResult<TenantsDO> page) {

        if ( page == null ) {
            return null;
        }

        PageResult<TenantsRespVO> pageResult = new PageResult<TenantsRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    public static List<TenantsRespVO> convertList(List<TenantsDO> list) {
    if (list == null) {
        return Collections.emptyList();
    }

    List<TenantsRespVO> resList = list.stream()
            .map(TenantsConvert::convert) // 假设转换方法为 TenantsConvert.convert
            .collect(Collectors.toList());

    return resList; // 可选：返回不可变列表
}


    public static TenantsRespVO convert(TenantsDO tenant) {
        TenantsRespVO vo = new TenantsRespVO();
        vo.setId(tenant.getId());
        vo.setName(tenant.getName());
        vo.setEncryptPublicKey(tenant.getEncryptPublicKey());
        vo.setPlan(tenant.getPlan());
        vo.setStatus(tenant.getStatus());
        vo.setUpdatedAt(tenant.getUpdatedAt() != null ? tenant.getUpdatedAt().format(formatter) : null);
        vo.setCreatedAt(tenant.getCreatedAt() != null ? tenant.getCreatedAt().format(formatter) : null);
        vo.setCustomConfig(tenant.getCustomConfig());
        return vo;
    }

}
