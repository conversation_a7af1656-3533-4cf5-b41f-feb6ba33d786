package com.szcares.aps.module.app.controller.admin.pricing;

import com.szcares.aps.framework.common.pojo.CommonResult;
import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.module.app.controller.admin.pricing.vo.PricingRuleLogPageReqVO;
import com.szcares.aps.module.app.dal.dataobject.pricing.PricingRuleLogDO;
import com.szcares.aps.module.app.service.pricing.PricingRuleLogsService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 定价规则日志查询接口
 */
@Tag(name = "管理后台 - 定价规则日志")
@RestController
@RequestMapping("/app/pricingruleLog")
@Validated
public class PricingRuleLogController {

    @Resource
    private PricingRuleLogsService pricingRuleLogsService;

    /**
     * 查询指定定价规则的日志列表
     * @return 日志列表
     */
    @GetMapping("/page")
    public CommonResult<PageResult<PricingRuleLogDO>> getLogPage(PricingRuleLogPageReqVO reqVO) {
        return CommonResult.success(pricingRuleLogsService.getLogPage(reqVO));
    }
}