<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.szcares.aps.module.app.dal.postgresql.report.ExpenseReportMapper">

    <select id="getAppQuotaRankingData" resultType="com.szcares.aps.module.app.controller.admin.report.vo.GetAppQuotaRankingDataRes">
        SELECT
        RANK() OVER (ORDER BY combined.total_cost DESC) AS ranking,
        combined.app_id,
        combined.total_cost,
        combined.message_cost,
        combined.workflow_cost,
        combined.record_num,
        a.name,
        a.mode,
        acc.name AS account_name,
        stats.use_num
        FROM (
        SELECT
        COALESCE(msg.app_id, wf.app_id) AS app_id,
        COALESCE(msg.message_cost, 0) AS message_cost,
        COALESCE(wf.workflow_cost, 0) AS workflow_cost,
        COALESCE(msg.message_num, 0) + COALESCE(wf.workflow_num, 0) AS record_num,
        COALESCE(msg.message_cost, 0) + COALESCE(wf.workflow_cost, 0) AS total_cost
        FROM (
        SELECT
        app_id,
        COUNT(id) AS message_num,
        SUM(total_price) AS message_cost
        FROM public.messages
        GROUP BY app_id
        ) msg
        FULL OUTER JOIN (
        SELECT
        app_id,
        COUNT(id) AS workflow_num,
        SUM(CAST((execution_metadata::json->>'total_price') AS NUMERIC)) AS workflow_cost
        FROM public.workflow_node_executions
        WHERE execution_metadata IS NOT NULL
        AND execution_metadata != ''
        AND (execution_metadata::json->>'total_price') IS NOT NULL
        GROUP BY app_id
        ) wf ON msg.app_id = wf.app_id
        ) combined
        LEFT JOIN apps a ON combined.app_id = a.id
        LEFT JOIN (
        SELECT
        t.id AS tenant_id,
        ta.account_id
        FROM tenants t
        JOIN tenant_account_joins ta ON t.id = ta.tenant_id
        WHERE ta.role = 'owner'
        ) tenant_info ON a.tenant_id = tenant_info.tenant_id
        LEFT JOIN accounts acc ON tenant_info.account_id = acc.id
        LEFT JOIN (
        SELECT
        app_id,
        SUM(number) AS use_num
        FROM app_statistics_extend
        GROUP BY app_id
        ) stats ON combined.app_id = stats.app_id
        ORDER BY combined.total_cost DESC
        LIMIT #{pageSize} OFFSET #{offset}
    </select>


    <select id="getAppTokenQuotaRankingData" resultType="com.szcares.aps.module.app.controller.admin.report.vo.GetAppTokenQuotaRankingDataRes">
        SELECT
        ROW_NUMBER() OVER (ORDER BY token_money.accumulated_quota DESC) AS ranking,
        app_info.app_name AS name,
        token_info.token AS app_token,
        token_money.accumulated_quota,
        token_money.day_used_quota,
        token_money.month_used_quota,
        token_money.day_limit_quota,
        token_money.month_limit_quota
        FROM (
        SELECT
        atm.app_token_id,
        atm.accumulated_quota,
        atm.day_used_quota,
        atm.month_used_quota,
        atm.day_limit_quota,
        atm.month_limit_quota
        FROM api_token_money_extend atm
        ) token_money
        JOIN (
        SELECT
        at.id AS app_token_id,
        at.app_id,
        at.token
        FROM api_tokens at
        ) token_info ON token_money.app_token_id = token_info.app_token_id
        JOIN (
        SELECT
        a.id AS app_id,
        a.name AS app_name
        FROM apps a
        ) app_info ON token_info.app_id = app_info.app_id
        ORDER BY
        token_money.accumulated_quota DESC
        LIMIT #{pageSize} OFFSET #{offset}
    </select>

    <select id="getAppTokenDailyQuotaData" resultType="com.szcares.aps.module.app.controller.admin.report.vo.GetAppTokenDailyQuotaDataRes">
        WITH date_range AS (
            SELECT generate_series::DATE AS stat_date
            FROM generate_series(
                CURRENT_DATE - INTERVAL '7 days',
                CURRENT_DATE,
                '1 day'
            )
        ),
        msg_data AS (
            SELECT
                to_char(m.created_at, 'yyyy-mm-dd')::DATE AS stat_date,
                SUM(m.total_price) AS total_cost
            FROM messages m
            WHERE m.total_price != 0
            AND m.created_at >= CURRENT_DATE - INTERVAL '7 days'
            GROUP BY to_char(m.created_at, 'yyyy-mm-dd')
        )
        SELECT
            to_char(dr.stat_date,'yyyy-mm-dd'),
            COALESCE(SUM(md.total_cost), 0.00) AS total_used
        FROM date_range dr
        LEFT JOIN msg_data md ON dr.stat_date = md.stat_date
        GROUP BY dr.stat_date
        ORDER BY dr.stat_date
    </select>

    <select id="getAiImageQuotaRankingData" resultType="com.szcares.aps.module.app.controller.admin.report.vo.GetAiImageQuotaRankingRes">
        SELECT
        ROW_NUMBER() OVER (ORDER BY SUM(account_layover_record_extend.money) DESC) AS ranking,
        forwarding_extend.address,
        forwarding_extend.path,
        SUM(account_layover_record_extend.money) AS total_cost,
        COUNT(*) AS record_num,
        account_layover_record_extend.info->>'model' AS model
        FROM
        account_layover_record_extend
        RIGHT JOIN
        accounts ON account_layover_record_extend.account_id = accounts.id
        RIGHT JOIN
        forwarding_extend ON account_layover_record_extend.forwarding_id = forwarding_extend.id
        WHERE
        (#{startDate} IS NULL OR account_layover_record_extend.created_at BETWEEN #{startDate} AND #{endDate})
        GROUP BY
        forwarding_extend.id, forwarding_extend.address, forwarding_extend.path,
        account_layover_record_extend.info->>'model'
        HAVING
        SUM(account_layover_record_extend.money) > 0
        ORDER BY
        ranking ASC
        LIMIT #{pageSize} OFFSET #{offset}
    </select>

    <select id="selectMessagesForExport" resultType="com.szcares.aps.module.app.controller.admin.report.vo.MessageExportDTO">
        SELECT
        m.app_id,
        CASE
        WHEN m.from_source = 'api' THEN t."name"
        ELSE a.email
        END AS name,
        CASE
        WHEN m.from_source = 'api' THEN t."name"
        ELSE ta_tenant."name"
        END AS tenant_name,
        a2."name" as app_name,
        m.model_provider,
        m.model_id,
        m.message_tokens,
        m.message_unit_price,
        m.answer_tokens,
        m.answer_unit_price,
        m.total_price,
        m.currency,
        m.from_source,
        m.from_end_user_id,
        m.from_account_id,
        m.created_at,
        m.workflow_run_id,
        m.status,
        m.invoke_from
        FROM
        messages m
        LEFT JOIN
        accounts a ON a.id = m.from_account_id AND m.from_source != 'api'
        LEFT JOIN
        end_users eu ON eu.id = m.from_end_user_id AND m.from_source = 'api'
        LEFT JOIN
        tenants t ON t.id = eu.tenant_id AND m.from_source = 'api'
        LEFT JOIN
        tenant_account_joins taj ON taj.account_id = m.from_account_id AND m.from_source != 'api'
        LEFT JOIN
        tenants ta_tenant ON ta_tenant.id = taj.tenant_id AND m.from_source != 'api'
        LEFT JOIN
        apps a2 ON a2.id = m.app_id
        <where>
            <if test="tenantName != null and tenantName != ''">
                (
                (m.from_source = 'api' AND t."name" LIKE CONCAT('%', #{tenantName}, '%')) OR
                (m.from_source != 'api' AND ta_tenant."name" LIKE CONCAT('%', #{tenantName}, '%'))
                )
            </if>
        </where>
        ORDER BY m.created_at DESC
    </select>

    <select id="findResult" resultType="com.szcares.aps.module.app.controller.admin.quoto.vo.GetQuotaManagementDataResponse">
        SELECT
        ame.account_id,
        RANK() OVER (ORDER BY ame.used_quota DESC) AS ranking,
        a.name AS account_name,
        ame.used_quota,
        ame.total_quota
        FROM public.account_money_extend ame
        JOIN accounts a ON ame.account_id = a.id
        <where>
            <if test="req.keyword != null and req.keyword.trim() != ''">
                <bind name="likeKeyword" value="'%' + req.keyword + '%'" />
                (a.name LIKE #{likeKeyword} OR a.email LIKE #{likeKeyword})
            </if>
        </where>
        ORDER BY ranking
        LIMIT #{req.pageSize} OFFSET #{offset}
    </select>

    <select id="countResult" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM public.account_money_extend ame
        JOIN accounts a ON ame.account_id = a.id
        WHERE 1=1
        <if test="req.keyword != null and req.keyword != ''">
            AND (a.name ILIKE '%' || #{req.keyword} || '%' OR a.email ILIKE '%' || #{req.keyword} || '%')
        </if>
    </select>

</mapper>