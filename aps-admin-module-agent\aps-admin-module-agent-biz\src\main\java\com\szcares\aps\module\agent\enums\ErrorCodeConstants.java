package com.szcares.aps.module.agent.enums;

import com.szcares.aps.framework.common.exception.ErrorCode;

/**
 * Agent 模块错误码枚举类
 * 
 * agent 模块，使用 2-003-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== 员工入职离职登记表 2-003-001-000 ==========
    ErrorCode ENTRY_EXIT_NOT_EXISTS = new ErrorCode(2_003_001_000, "员工入职离职记录不存在");

    // ========== 员工考勤登记表 2-003-002-000 ==========
    ErrorCode ATTENDANCE_NOT_EXISTS = new ErrorCode(2_003_002_000, "员工考勤记录不存在");

}