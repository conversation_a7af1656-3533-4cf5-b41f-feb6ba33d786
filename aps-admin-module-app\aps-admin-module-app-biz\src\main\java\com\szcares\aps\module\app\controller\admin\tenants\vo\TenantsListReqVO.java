package com.szcares.aps.module.app.controller.admin.tenants.vo;

import com.szcares.aps.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 租户列表 Request VO")
@Data
public class TenantsListReqVO  extends PageParam {

    @Schema(description = "租户名称，模糊匹配")
    private String name;

    @Schema(description = "展示状态，参见 CommonStatusEnum 枚举类", example = "1")
    private String status;

}
