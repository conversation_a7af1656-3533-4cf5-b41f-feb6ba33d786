package com.szcares.aps.module.agent.dal.mysql.attendance;

import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.framework.mybatis.core.mapper.BaseMapperX;
import com.szcares.aps.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.szcares.aps.module.agent.controller.admin.attendance.vo.AttendancePageReqVO;
import com.szcares.aps.module.agent.dal.dataobject.attendance.AttendanceDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 员工考勤登记表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AttendanceMapper extends BaseMapperX<AttendanceDO> {

    default PageResult<AttendanceDO> selectPage(AttendancePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AttendanceDO>()
                .likeIfPresent(AttendanceDO::getEmployeeName, reqVO.getEmployeeName())
                .eqIfPresent(AttendanceDO::getAttendanceDate, reqVO.getAttendanceDate())
                .eqIfPresent(AttendanceDO::getDepartment, reqVO.getDepartment())
                .betweenIfPresent(AttendanceDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AttendanceDO::getId));
    }

    default AttendanceDO selectByEmployeeNameAndAttendanceDate(String employeeName, String attendanceDate) {
        return selectOne(new LambdaQueryWrapperX<AttendanceDO>()
                .eq(AttendanceDO::getEmployeeName, employeeName)
                .eq(AttendanceDO::getAttendanceDate, attendanceDate));
    }

    default List<AttendanceDO> selectList(AttendancePageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AttendanceDO>()
                .likeIfPresent(AttendanceDO::getEmployeeName, reqVO.getEmployeeName())
                .eqIfPresent(AttendanceDO::getAttendanceDate, reqVO.getAttendanceDate())
                .eqIfPresent(AttendanceDO::getDepartment, reqVO.getDepartment())
                .betweenIfPresent(AttendanceDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AttendanceDO::getId));
    }

}