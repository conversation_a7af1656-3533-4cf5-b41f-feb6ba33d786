package com.szcares.aps.module.app.controller.admin.tenantQuoto;


import com.szcares.aps.framework.common.pojo.CommonResult;
import com.szcares.aps.module.app.controller.admin.tenantQuoto.vo.GetTenantQuotaManagementDataResponse;
import com.szcares.aps.module.app.controller.admin.tenantQuoto.vo.GetTenantQuotaRankingDataReq;
import com.szcares.aps.module.app.controller.admin.tenantQuoto.vo.SetTenantQuotaReq;
import com.szcares.aps.module.app.controller.admin.tenantQuoto.vo.PageResult;
import com.szcares.aps.module.app.service.tenantQuoto.TenantQuotaManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.szcares.aps.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 租户额度管理")
@RestController
@RequestMapping("/app/tenantQuota")
@Validated
public class TenantQuotaManagementController {

    @Resource
    private TenantQuotaManagementService quotaManagementService;

    @PutMapping("/setUserQuota")
    @Operation(summary = "设置租户额度")
    @PreAuthorize("@ss.hasPermission('system:quota:update')")
    public CommonResult<String> setUserQuota(@Valid @RequestBody SetTenantQuotaReq req){
        quotaManagementService.setTenantQuota(req);
           return CommonResult.success("设置成功");
    }

    @GetMapping("/quotaManagementList")
    @Operation(summary = "额度管理列表")
    @PreAuthorize("@ss.hasPermission('system:quota:query')")
    public CommonResult<PageResult<GetTenantQuotaManagementDataResponse>> getQuotaManagementList(
            @ModelAttribute GetTenantQuotaRankingDataReq pageInfo
    ){
        PageResult<GetTenantQuotaManagementDataResponse> result = quotaManagementService.getTenantQuotaManagementList(pageInfo);
        return success(result);
    }

}
