package com.szcares.aps.module.app.controller.admin.pricing.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class PricingRuleSaveReqVO {

    @Schema(description = "供应商", example = "openai")
    @NotBlank
    private String provider;

    @Schema(description = "模型名称", example = "gpt-4-turbo-2024-04-09")
    @NotBlank
    private String modelName;

    @Schema(description = "使用模式")
    private String usageMode;

    @Schema(description = "输入token价格/千token")
    @NotNull
    private BigDecimal inputPrice;

    @Schema(description = "输出token价格/千token")
    @NotNull
    private BigDecimal outputPrice;

    @Schema(description = "图像token价格/千token")
    private BigDecimal imagePrice;

    @Schema(description = "加价倍率")
    private BigDecimal markupRate;

    @Schema(description = "计价货币")
    private String currency;

    @Schema(description = "生效时间")
    private LocalDateTime effectiveDate;

    @Schema(description = "失效时间")
    private LocalDateTime expiryDate;

    @Schema(description = "创建人")
    private String createdBy;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "状态")
    private Integer status;
}