package com.szcares.aps.module.agent.dal.dataobject.attendance;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.szcares.aps.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 员工考勤登记表 DO
 *
 * <AUTHOR>
 */
@TableName("attendance_table_extend")
@KeySequence("attendance_table_extend_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttendanceDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    
    /**
     * 员工姓名
     */
    private String employeeName;
    
    /**
     * 考勤组
     */
    private String attendanceGroup;
    
    /**
     * 所属部门
     */
    private String department;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 考勤日期
     */
    private String attendanceDate;
    
    /**
     * 工作日期相关信息
     */
    private String workDate;
    
    /**
     * 班次，表明员工当天的工作班次安排
     */
    private String shift;
    
    /**
     * 最终下班打卡时间（根据优先级逻辑确定）
     */
    private String checkoutTime;
    
    /**
     * 最终下班打卡结果（根据优先级逻辑确定）
     */
    private String checkoutResult;
    
    /**
     * 关联的审批单
     */
    private String relatedApprovalForm;
    
    /**
     * 出差时长
     */
    private String businessTripDuration;
    
    /**
     * 外出时长
     */
    private String outingDuration;
    
    /**
     * 病假天数
     */
    private String sickLeaveDays;
    
    /**
     * 年假天数
     */
    private String annualLeaveDays;
    
    /**
     * 事假天数
     */
    private String personalLeaveDays;
    
    /**
     * 丧假天数
     */
    private String mourningLeaveDays;
    
    /**
     * 调休天数
     */
    private String compensatoryLeaveDays;
    
    /**
     * 探亲假天数
     */
    private String homeVisitLeaveDays;
    
    /**
     * 产假天数
     */
    private String maternityLeaveDays;
    
    /**
     * 陪产假天数
     */
    private String paternityLeaveDays;
    
    /**
     * 婚假天数
     */
    private String marriageLeaveDays;
    
    /**
     * 哺乳假天数
     */
    private String nursingLeaveDays;
    
    /**
     * 育儿假天数
     */
    private String childcareLeaveDays;
    
    /**
     * 护理假天数
     */
    private String careLeaveDays;
    
    /**
     * 例假天数
     */
    private String menstrualLeaveDays;
    
    /**
     * 加班总时长
     */
    private String totalOvertimeDuration;
    
    /**
     * 工作日（转调休）
     */
    private String workingDayCompensatoryLeave;
    
    /**
     * 休息日（转调休）
     */
    private String restDayCompensatoryLeave;
    
    /**
     * 节假日（转调休）
     */
    private String holidayCompensatoryLeave;

}