package com.szcares.aps.module.app.util;

import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.security.spec.KeySpec;
import java.util.Base64;

public class PasswordUtil {


    // 生成盐
    public static byte[] generateSalt(int length) {
        byte[] salt = new byte[length];
        new SecureRandom().nextBytes(salt);
        return salt;
    }

    // 密码加密（返回16进制字符串，与Python一致）
    public static String hashPasswordHex(String password, byte[] salt) {
        try {
            int iterations = 10000;
            int keyLength = 256;
            KeySpec spec = new PBEKeySpec(password.toCharArray(), salt, iterations, keyLength);
            SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");
            byte[] dk = factory.generateSecret(spec).getEncoded();

            String hex = bytesToHex(dk);
            String base64 = Base64.getEncoder().encodeToString(hex.getBytes(StandardCharsets.UTF_8));
            return base64;
        } catch (Exception e) {
            throw new RuntimeException("Error hashing password", e);
        }
    }

    // 字节数组转16进制字符串
    public static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    // 16进制字符串转字节数组
    public static byte[] hexToBytes(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                    + Character.digit(hex.charAt(i+1), 16));
        }
        return data;
    }

    // 密码比对（与Python一致）
//    public static boolean comparePassword(String password, String passwordHashedHex, String saltBase64) {
//        byte[] salt = java.util.Base64.getDecoder().decode(saltBase64);
//        String hashed = hashPasswordHex(password, salt);
//        return hashed.equalsIgnoreCase(passwordHashedHex);
//    }

}