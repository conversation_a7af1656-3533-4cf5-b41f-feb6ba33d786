package com.szcares.aps.framework.ip.core.utils;

import com.szcares.aps.framework.ip.core.Area;
import org.junit.jupiter.api.Test;
import org.lionsoul.ip2region.xdb.Searcher;


import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * {@link IPUtils} 的单元测试
 *
 * <AUTHOR>
 */
public class IPUtilsTest {

    @Test
    public void testGetAreaId_string() {
        // ***********|*************|420600
        Integer areaId = IPUtils.getAreaId("************");
        assertEquals(420600, areaId);
    }

    @Test
    public void testGetAreaId_long() throws Exception {
        // *************|***************|360900
        long ip = Searcher.checkIP("***************");
        Integer areaId = IPUtils.getAreaId(ip);
        assertEquals(360900, areaId);
    }

    @Test
    public void testGetArea_string() {
        // ***********|*************|420600
        Area area = IPUtils.getArea("************");
        assertEquals("襄阳市", area.getName());
    }

    @Test
    public void testGetArea_long() throws Exception {
        // *************|***************|360900
        long ip = Searcher.checkIP("***************");
        Area area = IPUtils.getArea(ip);
        assertEquals("宜春市", area.getName());
    }

}
