package com.szcares.aps.module.app.controller.admin.quoto.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class GetQuotaManagementDataResponse {

    @Schema(description = "用户id")
    private String id;

    @Schema(description = "排名")
    private int ranking;

    @Schema(description = "用户名称")
    private String name;

    @Schema(description = "已用额度")
    private BigDecimal usedQuota;

    @Schema(description = "总额度")
    private BigDecimal totalQuota;

    public GetQuotaManagementDataResponse(String id, int ranking, String name, BigDecimal usedQuota, BigDecimal totalQuota) {
        this.id = id;
        this.ranking = ranking;
        this.name = name;
        this.usedQuota = usedQuota;
        this.totalQuota = totalQuota;
    }


}
