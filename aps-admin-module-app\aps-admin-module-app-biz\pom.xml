<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.szcares.aps</groupId>
        <artifactId>aps-admin-module-app</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>aps-admin-module-app-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        app 模块
    </description>

    <dependencies>
        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-env</artifactId>
        </dependency>

        <!-- 依赖服务 -->
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-module-app-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-biz-ip</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-rpc</artifactId>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-job</artifactId>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-mq</artifactId>
        </dependency>

        <!-- 服务保障相关 TODO 芋艿：暂时去掉 -->
        <!--        <dependency>-->
        <!--            <groupId>com.szcares.aps</groupId>-->
        <!--            <artifactId>aps-admin-spring-boot-starter-protection</artifactId>-->
        <!--        </dependency>-->

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <!-- 监控相关 -->
        <dependency>
            <groupId>com.szcares.aps</groupId>
            <artifactId>aps-admin-spring-boot-starter-monitor</artifactId>
        </dependency>

        <!--  Jackson序列化 -->
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>

        <!-- 三方云服务相关 -->
        <dependency>
            <groupId>me.zhyd.oauth</groupId>
            <artifactId>JustAuth</artifactId> <!-- 社交登陆（例如说，个人微信、企业微信等等） -->
        </dependency>
        <dependency>
            <groupId>com.xkcoding.justauth</groupId>
            <artifactId>justauth-spring-boot-starter</artifactId>
            <exclusions>
                <!-- 移除，避免和项目里的 hutool-all 冲突 -->
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>wx-java-mp-spring-boot-starter</artifactId> <!-- 微信登录（公众号） -->
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>wx-java-miniapp-spring-boot-starter</artifactId>  <!-- 微信登录（小程序） -->
        </dependency>

        <dependency>
            <groupId>com.anji-plus</groupId>
            <artifactId>captcha-spring-boot-starter</artifactId> <!-- 验证码，一般用于登录使用 -->
        </dependency>

        <dependency>
            <groupId>org.dromara.hutool</groupId>
            <artifactId>hutool-extra</artifactId> <!-- 邮件 -->
        </dependency>

        <!-- JJWT 核心库 -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>0.11.5</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>0.11.5</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId> <!-- 用于 JSON 处理 -->
            <version>0.11.5</version>
            <scope>runtime</scope>
        </dependency>
        <!-- Base64 编解码工具 -->
<!--        <dependency>-->
<!--            <groupId>commons-codec</groupId>-->
<!--            <artifactId>commons-codec</artifactId>-->
<!--            <version>1.15</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.fasterxml.jackson.datatype</groupId>-->
<!--            <artifactId>jackson-datatype-jsr310</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.fasterxml.jackson.core</groupId>-->
<!--            <artifactId>jackson-databind</artifactId>-->
<!--            <version>2.15.2</version> &lt;!&ndash; 使用最新稳定版本 &ndash;&gt;-->
<!--        </dependency>-->
    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
                <!-- 子模块排除 spring-boot-maven-plugin 注意！！！重点是下面 3 行！！！ -->
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
