package com.szcares.aps.module.app.api.license.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Schema(description = "RPC 服务 - 租户授权 Response DTO")
@Data
public class AppLicenseRespDTO {

    @Schema(description = "租户授权码唯一标识")
    private Long id;

    /**
     * 部署租户的id
     */
    @Schema(description = "部署的租户ID")
    private String tenantsId;

    /**
     * 授权数据json
     */
    @Schema(description = "授权配置原始JSON数据")
    private String authorizeJson;

    /**
     * 授权数据加密json
     */
    @Schema(description = "加密后的授权配置JSON数据")
    private String encryptedJson;

    /**
     * 部署用户名
     */
    @Schema(description = "部署操作的用户名")
    private String userName;

    /**
     * 有效期
     */
    @Schema(description = "授权码有效截止日期")
    private LocalDateTime expirationDate;

    /**
     * 授权码使用状态，0：未使用；1：已使用；2：过期
     */
    @Schema(description = "授权码使用状态(0:未使用 1:已使用 2:已过期)")
    private String status;

    /**
     * 备注
     */
    @Schema(description = "授权码备注信息")
    private String remark;

    /**
     * 创建者
     */
    @Schema(description = "记录创建人")
    private String creator;

    /**
     * 创建时间
     */
    @Schema(description = "记录创建时间")
    private Date createTime;

    /**
     * 更新者
     */
    @Schema(description = "记录最后更新人")
    private String updater;

    /**
     * 更新时间
     */
    @Schema(description = "记录最后更新时间")
    private Date updateTime;

    /**
     * 是否删除
     */
    @Schema(description = "逻辑删除标记(true:已删除 false:未删除)")
    private Boolean deleted;

}
