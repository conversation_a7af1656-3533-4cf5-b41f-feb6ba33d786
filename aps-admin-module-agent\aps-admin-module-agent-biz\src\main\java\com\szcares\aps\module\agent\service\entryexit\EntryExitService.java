package com.szcares.aps.module.agent.service.entryexit;

import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.module.agent.controller.admin.entryexit.vo.EntryExitExportExcelVO;
import com.szcares.aps.module.agent.controller.admin.entryexit.vo.EntryExitImportExcelVO;
import com.szcares.aps.module.agent.controller.admin.entryexit.vo.EntryExitImportRespVO;
import com.szcares.aps.module.agent.controller.admin.entryexit.vo.EntryExitPageReqVO;
import com.szcares.aps.module.agent.controller.admin.entryexit.vo.EntryExitRespVO;
import com.szcares.aps.module.agent.controller.admin.entryexit.vo.EntryExitSaveReqVO;
import com.szcares.aps.module.agent.dal.dataobject.entryexit.EntryExitDO;

import java.util.List;

/**
 * 员工入职离职登记表 Service 接口
 *
 * <AUTHOR>
 */
public interface EntryExitService {

    /**
     * 创建员工入职离职登记表
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createEntryExit(EntryExitSaveReqVO createReqVO);

    /**
     * 更新员工入职离职登记表
     *
     * @param updateReqVO 更新信息
     */
    void updateEntryExit(EntryExitSaveReqVO updateReqVO);

    /**
     * 删除员工入职离职登记表
     *
     * @param id 编号
     */
    void deleteEntryExit(Long id);

    /**
     * 获得员工入职离职登记表
     *
     * @param id 编号
     * @return 员工入职离职登记表
     */
    EntryExitDO getEntryExit(Long id);

    /**
     * 获得员工入职离职登记表分页
     *
     * @param pageReqVO 分页查询
     * @return 员工入职离职登记表分页
     */
    PageResult<EntryExitRespVO> getEntryExitPage(EntryExitPageReqVO pageReqVO);

    /**
     * 获得员工入职离职登记表列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 员工入职离职登记表列表
     */
    List<EntryExitDO> getEntryExitList(EntryExitPageReqVO exportReqVO);

    /**
     * 获得员工入职离职登记表 Excel 导出列表
     *
     * @param exportReqVO 查询条件
     * @return 员工入职离职登记表 Excel 导出列表
     */
    List<EntryExitExportExcelVO> getEntryExitExportList(EntryExitPageReqVO exportReqVO);

    /**
     * 导入员工入职离职登记表 Excel
     *
     * @param importReqVO 导入信息
     * @return 导入结果
     */
    EntryExitImportRespVO importEntryExitList(List<EntryExitImportExcelVO> importReqVO);

}