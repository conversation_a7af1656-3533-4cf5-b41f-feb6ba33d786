package com.szcares.aps.module.app.convert.account;

import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.framework.common.util.object.BeanUtils;
import com.szcares.aps.module.app.controller.admin.account.vo.AccountRespVO;
import com.szcares.aps.module.app.dal.dataobject.account.AccountDO;

import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class AccountConvert {

    public static PageResult<AccountRespVO> convertAccountRespVO(PageResult<AccountDO> page) {

        if ( page == null ) {
            return null;
        }

        PageResult<AccountRespVO> pageResult = new PageResult<AccountRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    public static List<AccountRespVO> convertList(List<AccountDO> list) {
        if (list == null) {
            return Collections.emptyList();
        }
        List<AccountRespVO> resList = BeanUtils.toBean(list, AccountRespVO.class);
        return resList;
    }

}
