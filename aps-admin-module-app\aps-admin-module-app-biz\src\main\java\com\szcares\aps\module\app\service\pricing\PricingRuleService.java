package com.szcares.aps.module.app.service.pricing;

import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.module.app.controller.admin.pricing.vo.*;
import com.szcares.aps.module.app.dal.dataobject.pricing.PricingRuleDO;

public interface PricingRuleService {

    Long createPricingRule(PricingRuleSaveReqVO createReqVO);

    void updatePricingRule(PricingRuleUpdateReqVO updateReqVO);

    void deletePricingRule(Long id);

    PageResult<PricingRuleDO> getPricingRulePageList(PricingRulePageReqVO reqVO);

    PricingRuleDO getPricingRule(Long id);
}