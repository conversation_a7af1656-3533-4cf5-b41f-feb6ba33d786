package com.szcares.aps.module.agent.controller.admin.entryexit.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 员工入职离职登记表 Response VO")
@Data
public class EntryExitRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "员工姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String employeeName;

    @Schema(description = "变动类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "入职")
    private String movementType;

    @Schema(description = "变动日期", example = "2024-01-01")
    private String movementDate;

    @Schema(description = "年份", example = "2024")
    private Integer year;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}