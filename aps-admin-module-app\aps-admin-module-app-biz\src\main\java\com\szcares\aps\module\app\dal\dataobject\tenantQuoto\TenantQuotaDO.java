package com.szcares.aps.module.app.dal.dataobject.tenantQuoto;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 额度表
 *
 * <AUTHOR>
 */
@TableName("tenant_money_extend")
@Data
public class TenantQuotaDO {

    /**
     * 用户id
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 额度
     */
    private BigDecimal totalQuota;

    /**
     * 已使用额度
     */
    private BigDecimal usedQuota;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

}
