package com.szcares.aps.module.infra.dal.mysql.demo;

import java.util.*;

import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.framework.common.pojo.PageParam;
import com.szcares.aps.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.szcares.aps.framework.mybatis.core.mapper.BaseMapperX;
import com.szcares.aps.module.infra.dal.dataobject.demo.InfraStudentTeacherDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 学生班主任 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InfraStudentTeacherMapper extends BaseMapperX<InfraStudentTeacherDO> {

    default InfraStudentTeacherDO selectByStudentId(Long studentId) {
        return selectOne(InfraStudentTeacherDO::getStudentId, studentId);
    }

    default int deleteByStudentId(Long studentId) {
        return delete(InfraStudentTeacherDO::getStudentId, studentId);
    }

}