package com.szcares.aps.module.app.dal.mysql.license;

/**
 * <AUTHOR>
 * @date 2025/6/11 11:12
 */

import com.szcares.aps.framework.mybatis.core.mapper.BaseMapperX;
import com.szcares.aps.module.app.dal.dataobject.license.AgentLicenseDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface AgentLicenseMapper extends BaseMapperX<AgentLicenseDO> {
    // 继承 BaseMapper 已包含 CRUD，可扩展自定义 SQL
}
