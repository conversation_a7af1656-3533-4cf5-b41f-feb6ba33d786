package com.szcares.aps.module.app.controller.admin.tenants.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Schema(description = "管理后台 - 租户绑定 Request VO")
@Data
public class TenantsAccountReqVO {

    private String id;
    @Schema(description = "租户", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "租户不能为空")
    private String tenantId;
    @NotBlank(message = "用户不能为空")
    private String accountId;
    @NotBlank(message = "角色不能为空")
    private String role;
    private String invitedBy;
    private boolean current;
}
