package com.szcares.aps.module.app.service.pricing.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.framework.security.core.util.SecurityFrameworkUtils;
import com.szcares.aps.module.app.controller.admin.pricing.vo.PricingRulePageReqVO;
import com.szcares.aps.module.app.controller.admin.pricing.vo.PricingRuleSaveReqVO;
import com.szcares.aps.module.app.controller.admin.pricing.vo.PricingRuleUpdateReqVO;
import com.szcares.aps.module.app.convert.pricing.PricingRuleConvert;
import com.szcares.aps.module.app.dal.dataobject.pricing.PricingRuleDO;
import com.szcares.aps.module.app.dal.dataobject.pricing.PricingRuleLogDO;
import com.szcares.aps.module.app.dal.postgresql.pricing.PricingRuleLogMapper;
import com.szcares.aps.module.app.dal.postgresql.pricing.PricingRuleMapper;
import com.szcares.aps.module.app.service.pricing.PricingRuleService;
import com.szcares.aps.module.app.util.BeanDiffUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;

@Service
public class PricingRuleServiceImpl implements PricingRuleService {

    @Resource
    private PricingRuleMapper pricingRuleMapper;

    @Resource
    private PricingRuleLogMapper pricingRuleLogMapper;

    private final ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());

    @Override
    @DS("apsds")
    public Long createPricingRule(PricingRuleSaveReqVO createReqVO) {
        PricingRuleDO entity = PricingRuleConvert.convertPricingRuleDO(createReqVO);
        entity.setCreatedAt(LocalDateTime.now());
        entity.setUpdatedAt(LocalDateTime.now());
        entity.setCreatedBy(SecurityFrameworkUtils.getLoginUserId().toString());
        pricingRuleMapper.insert(entity);

        // 记录日志
        PricingRuleLogDO log = new PricingRuleLogDO();
        log.setOperator(entity.getCreatedBy());
        log.setOperateTime(LocalDateTime.now());
        log.setOperateType("CREATE");
        try {
            log.setOperateContent(objectMapper.writeValueAsString(entity));
        } catch (Exception e) {
            log.setOperateContent("序列化失败"+e.toString());
        }
        pricingRuleLogMapper.insert(log);

        return entity.getId();
    }

    @Override
    @DS("apsds")
    public void updatePricingRule(PricingRuleUpdateReqVO updateReqVO) {
        PricingRuleDO oldEntity = pricingRuleMapper.selectById(updateReqVO.getId());
        PricingRuleDO entity = PricingRuleConvert.convertPricingRuleDO(updateReqVO);
        entity.setUpdatedAt(LocalDateTime.now());
        pricingRuleMapper.updateById(entity);

        // 记录日志
        PricingRuleLogDO log = new PricingRuleLogDO();
        log.setOperator(SecurityFrameworkUtils.getLoginUserId().toString());
        log.setOperateTime(LocalDateTime.now());
        log.setOperateType("UPDATE");
        try {
            // 比较差异（这里只做简单示例，建议用 Map 存储差异字段）
            Map<String, String> diff = BeanDiffUtil.diff(oldEntity, entity);
            log.setOperateContent(objectMapper.writeValueAsString(diff));
        } catch (Exception e) {
            log.setOperateContent("序列化失败");
        }
        pricingRuleLogMapper.insert(log);
    }

    @Override
    @DS("apsds")
    public void deletePricingRule(Long id) {
        pricingRuleMapper.deleteById(id);
        // 记录日志
        PricingRuleLogDO log = new PricingRuleLogDO();
        log.setOperator(SecurityFrameworkUtils.getLoginUserId().toString());
        log.setOperateTime(LocalDateTime.now());
        log.setOperateType("DELETE");
        log.setOperateContent("删除ID: " + id);
        pricingRuleLogMapper.insert(log);
    }

    @Override
    @DS("apsds")
    public PageResult<PricingRuleDO> getPricingRulePageList(PricingRulePageReqVO reqVO) {
        PageResult<PricingRuleDO> list = pricingRuleMapper.selectPage(reqVO);
        return list;
    }
    
    @Override
    @DS("apsds")
    public PricingRuleDO getPricingRule(Long id) {
        return pricingRuleMapper.selectById(id);
    }
}