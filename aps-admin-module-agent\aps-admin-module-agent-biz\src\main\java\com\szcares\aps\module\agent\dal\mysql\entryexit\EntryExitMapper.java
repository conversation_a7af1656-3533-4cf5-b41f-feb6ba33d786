package com.szcares.aps.module.agent.dal.mysql.entryexit;

import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.framework.mybatis.core.mapper.BaseMapperX;
import com.szcares.aps.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.szcares.aps.module.agent.controller.admin.entryexit.vo.EntryExitPageReqVO;
import com.szcares.aps.module.agent.dal.dataobject.entryexit.EntryExitDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 员工入职离职登记表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface EntryExitMapper extends BaseMapperX<EntryExitDO> {

    default PageResult<EntryExitDO> selectPage(EntryExitPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<EntryExitDO>()
                .likeIfPresent(EntryExitDO::getEmployeeName, reqVO.getEmployeeName())
                .eqIfPresent(EntryExitDO::getMovementType, reqVO.getMovementType())
                .likeIfPresent(EntryExitDO::getMovementDate, reqVO.getMovementDate())
                .eqIfPresent(EntryExitDO::getYear, reqVO.getYear())
                .orderByDesc(EntryExitDO::getId));
    }

    default List<EntryExitDO> selectList(EntryExitPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<EntryExitDO>()
                .likeIfPresent(EntryExitDO::getEmployeeName, reqVO.getEmployeeName())
                .eqIfPresent(EntryExitDO::getMovementType, reqVO.getMovementType())
                .likeIfPresent(EntryExitDO::getMovementDate, reqVO.getMovementDate())
                .eqIfPresent(EntryExitDO::getYear, reqVO.getYear())
                .orderByDesc(EntryExitDO::getId));
    }

    default EntryExitDO selectByEmployeeName(String employeeName) {
        return selectOne(EntryExitDO::getEmployeeName, employeeName);
    }

}