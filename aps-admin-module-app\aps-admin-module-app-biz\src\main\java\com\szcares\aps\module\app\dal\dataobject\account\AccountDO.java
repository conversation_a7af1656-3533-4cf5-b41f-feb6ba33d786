package com.szcares.aps.module.app.dal.dataobject.account;

import com.baomidou.mybatisplus.annotation.*;
import com.fhs.core.trans.vo.TransPojo;
import com.szcares.aps.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 账户表
 *
 * <AUTHOR>
 */
@TableName("accounts")
@Data
public class AccountDO implements Serializable, TransPojo {

    public static final Long PARENT_ID_ROOT = 0L;

    private String id;

    private String name;

    private String email;

    private String password;

    private String passwordSalt;

    private String avatar;

    private String interfaceLanguage;

    private String interfaceTheme;

    private String timezone;

    private String lastLoginIp;

    private LocalDateTime lastLoginAt;

    private String status; // 'active'::character varying

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    @TableField(exist = false)
    private String role;

}
