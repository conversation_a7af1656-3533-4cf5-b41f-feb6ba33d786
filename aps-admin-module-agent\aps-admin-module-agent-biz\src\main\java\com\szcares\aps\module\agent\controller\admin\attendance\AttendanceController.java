package com.szcares.aps.module.agent.controller.admin.attendance;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.szcares.aps.framework.common.pojo.CommonResult;
import com.szcares.aps.framework.common.pojo.PageParam;
import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.framework.excel.core.util.ExcelUtils;
import com.szcares.aps.module.agent.controller.admin.attendance.vo.*;
import com.szcares.aps.module.agent.convert.attendance.AttendanceConvert;
import com.szcares.aps.module.agent.dal.dataobject.attendance.AttendanceDO;
import com.szcares.aps.module.agent.framework.security.config.AttendanceExcelListener;
import com.szcares.aps.module.agent.service.attendance.AttendanceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import static com.szcares.aps.framework.common.pojo.CommonResult.success;
import static com.szcares.aps.framework.common.pojo.CommonResult.error;

@Tag(name = "管理后台 - 员工考勤登记表")
@RestController
@RequestMapping("/agent/attendance")
@Validated
@Slf4j
public class AttendanceController {

    @Resource
    private AttendanceService attendanceService;

    @PostMapping("/create")
    @Operation(summary = "创建员工考勤登记表")
//    @PreAuthorize("@ss.hasPermission('agent:attendance:create')")
    public CommonResult<Long> createAttendance(@Valid @RequestBody AttendanceSaveReqVO createReqVO) {
        return success(attendanceService.createAttendance(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新员工考勤登记表")
//    @PreAuthorize("@ss.hasPermission('agent:attendance:update')")
    public CommonResult<Boolean> updateAttendance(@Valid @RequestBody AttendanceSaveReqVO updateReqVO) {
        attendanceService.updateAttendance(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除员工考勤登记表")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('agent:attendance:delete')")
    public CommonResult<Boolean> deleteAttendance(@RequestParam("id") Long id) {
        attendanceService.deleteAttendance(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得员工考勤登记表")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('agent:attendance:query')")
    public CommonResult<AttendanceRespVO> getAttendance(@RequestParam("id") Long id) {
        AttendanceDO attendance = attendanceService.getAttendance(id);
        return success(AttendanceConvert.INSTANCE.convert(attendance));
    }

    @GetMapping("/page")
    @Operation(summary = "获得员工考勤登记表分页")
//    @PreAuthorize("@ss.hasPermission('agent:attendance:query')")
    public CommonResult<PageResult<AttendanceRespVO>> getAttendancePage(@Valid AttendancePageReqVO pageVO) {
        PageResult<AttendanceRespVO> pageResult = attendanceService.getAttendancePage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出员工考勤登记表 Excel")
//    @PreAuthorize("@ss.hasPermission('agent:attendance:export')")
//    @OperateLog(type = EXPORT)
    public void exportAttendanceExcel(@Valid AttendancePageReqVO pageVO,
                                      HttpServletResponse response) throws IOException {
        pageVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AttendanceExportExcelVO> list = attendanceService.getAttendanceExportList(pageVO);
        // 导出 Excel
        ExcelUtils.write(response, "员工考勤登记表.xls", "数据", AttendanceExportExcelVO.class, list);
    }

    @PostMapping("/import")
    @Operation(summary = "导入员工考勤登记表 Excel")
    @PermitAll
//    @PreAuthorize("@ss.hasPermission('agent:attendance:import')")
//    @OperateLog(type = IMPORT)
    public CommonResult<AttendanceImportRespVO> importAttendanceExcel(@RequestParam("file") MultipartFile file) throws Exception {
        try {
            log.info("开始导入Excel文件，文件名: {}", file.getOriginalFilename());

            // 方法1：使用自定义监听器
            AttendanceExcelListener listener = new AttendanceExcelListener();
            EasyExcel.read(file.getInputStream(), AttendanceImportExcelVO.class, listener)
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet(0)
                    .headRowNumber(2) // 跳过前两行标题
                    .autoTrim(true)
                    .doRead();

            List<AttendanceImportExcelVO> datas = listener.getDataList();
            log.info("使用监听器读取到的数据行数: {}", datas.size());

            // 如果监听器没有读取到数据，尝试不同的headRowNumber值
            if (datas.isEmpty()) {
                log.warn("监听器未读取到数据，尝试使用同步读取方式");

                // 尝试不同的headRowNumber值
                for (int headRowNumber = 0; headRowNumber <= 3; headRowNumber++) {
                    try {
                        log.info("尝试 headRowNumber = {}", headRowNumber);
                        datas = EasyExcel.read(file.getInputStream(), AttendanceImportExcelVO.class, null)
                                .excelType(ExcelTypeEnum.XLSX)
                                .sheet(0)
                                .headRowNumber(headRowNumber)
                                .autoTrim(true)
                                .doReadSync();

                        log.info("headRowNumber={} 时读取到的数据行数: {}", headRowNumber, datas.size());

                        if (!datas.isEmpty()) {
                            // 打印第一行数据进行验证
                            AttendanceImportExcelVO firstRow = datas.get(0);
                            log.info("第一行数据: 员工姓名={}, 考勤组={}, 部门={}, UserId={}",
                                    firstRow.getEmployeeName(), firstRow.getAttendanceGroup(),
                                    firstRow.getDepartment(), firstRow.getUserId());

                            // 如果第一行有有效数据，就使用这个设置
                            if (firstRow.getEmployeeName() != null && !firstRow.getEmployeeName().trim().isEmpty()) {
                                log.info("找到有效数据，使用 headRowNumber = {}", headRowNumber);
                                break;
                            }
                        }
                    } catch (Exception e) {
                        log.warn("headRowNumber={} 时读取失败: {}", headRowNumber, e.getMessage());
                    }
                }

                log.info("最终读取到的数据行数: {}", datas.size());

                // 打印前几行数据进行调试
                for (int i = 0; i < Math.min(3, datas.size()); i++) {
                    AttendanceImportExcelVO data = datas.get(i);
                    log.info("第{}行数据: 员工姓名={}, 考勤组={}, 部门={}, UserId={}",
                            i + 1, data.getEmployeeName(), data.getAttendanceGroup(),
                            data.getDepartment(), data.getUserId());
                }
            }

            if (datas.isEmpty()) {
                return error(400, "Excel文件中没有读取到有效数据，请检查文件格式");
            }

            return success(attendanceService.importAttendanceList(datas));
        } catch (Exception e) {
            log.error("Excel导入失败", e);
            return error(500, "Excel导入失败: " + e.getMessage());
        }
    }

    @GetMapping("/get-import-template")
    @Operation(summary = "获得员工考勤登记表导入模板")
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        List<AttendanceImportExcelVO> list = Arrays.asList(
                new AttendanceImportExcelVO()
                        .setEmployeeName("冯杰")
                        .setAttendanceGroup("研发弹性班制")
                        .setDepartment("研发部")
                        .setAttendanceDate("25-07-08 星期二")
                        .setShift("常规班 08:30-17:00")
                        .setWork1CheckinTime("08:28")
                        .setWork1CheckinResult("正常")
                        .setWork1CheckoutTime("17:30")
                        .setWork1CheckoutResult("正常"),
                new AttendanceImportExcelVO()
                        .setEmployeeName("胡蓉(离职)")
                        .setAttendanceGroup("研发弹性班制")
                        .setDepartment("研发部")
                        .setAttendanceDate("25-07-08 星期二")
                        .setShift("常规班 08:30-17:00")
                        .setWork1CheckinTime("08:30")
                        .setWork1CheckinResult("正常")
                        .setWork1CheckoutTime("17:00")
                        .setWork1CheckoutResult("正常")
                        .setWork2CheckoutTime("21:13")
                        .setWork2CheckoutResult("正常")
                        .setWork3CheckoutTime("外出07-08 08:30到07-08 12:45")
                        .setWork3CheckoutResult("0.5天")
        );
        ExcelUtils.write(response, "员工考勤登记表导入模板.xls", "员工考勤登记表", AttendanceImportExcelVO.class, list);
    }

}