package com.szcares.aps.module.agent.controller.admin.attendance;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.szcares.aps.framework.common.pojo.CommonResult;
import com.szcares.aps.framework.common.pojo.PageParam;
import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.framework.excel.core.util.ExcelUtils;
import com.szcares.aps.module.agent.controller.admin.attendance.vo.*;
import com.szcares.aps.module.agent.convert.attendance.AttendanceConvert;
import com.szcares.aps.module.agent.dal.dataobject.attendance.AttendanceDO;
import com.szcares.aps.module.agent.framework.security.config.GenericExcelListener;

import java.util.Map;
import java.util.ArrayList;
import com.szcares.aps.module.agent.service.attendance.AttendanceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import static com.szcares.aps.framework.common.pojo.CommonResult.success;
import static com.szcares.aps.framework.common.pojo.CommonResult.error;

@Tag(name = "管理后台 - 员工考勤登记表")
@RestController
@RequestMapping("/agent/attendance")
@Validated
@Slf4j
public class AttendanceController {

    @Resource
    private AttendanceService attendanceService;

    @PostMapping("/create")
    @Operation(summary = "创建员工考勤登记表")
//    @PreAuthorize("@ss.hasPermission('agent:attendance:create')")
    public CommonResult<Long> createAttendance(@Valid @RequestBody AttendanceSaveReqVO createReqVO) {
        return success(attendanceService.createAttendance(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新员工考勤登记表")
//    @PreAuthorize("@ss.hasPermission('agent:attendance:update')")
    public CommonResult<Boolean> updateAttendance(@Valid @RequestBody AttendanceSaveReqVO updateReqVO) {
        attendanceService.updateAttendance(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除员工考勤登记表")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('agent:attendance:delete')")
    public CommonResult<Boolean> deleteAttendance(@RequestParam("id") Long id) {
        attendanceService.deleteAttendance(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得员工考勤登记表")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('agent:attendance:query')")
    public CommonResult<AttendanceRespVO> getAttendance(@RequestParam("id") Long id) {
        AttendanceDO attendance = attendanceService.getAttendance(id);
        return success(AttendanceConvert.INSTANCE.convert(attendance));
    }

    @GetMapping("/page")
    @Operation(summary = "获得员工考勤登记表分页")
//    @PreAuthorize("@ss.hasPermission('agent:attendance:query')")
    public CommonResult<PageResult<AttendanceRespVO>> getAttendancePage(@Valid AttendancePageReqVO pageVO) {
        PageResult<AttendanceRespVO> pageResult = attendanceService.getAttendancePage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出员工考勤登记表 Excel")
//    @PreAuthorize("@ss.hasPermission('agent:attendance:export')")
//    @OperateLog(type = EXPORT)
    public void exportAttendanceExcel(@Valid AttendancePageReqVO pageVO,
                                      HttpServletResponse response) throws IOException {
        pageVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AttendanceExportExcelVO> list = attendanceService.getAttendanceExportList(pageVO);
        // 导出 Excel
        ExcelUtils.write(response, "员工考勤登记表.xls", "数据", AttendanceExportExcelVO.class, list);
    }

    @PostMapping("/import")
    @Operation(summary = "导入员工考勤登记表 Excel")
    @PermitAll
//    @PreAuthorize("@ss.hasPermission('agent:attendance:import')")
//    @OperateLog(type = IMPORT)
    public CommonResult<AttendanceImportRespVO> importAttendanceExcel(@RequestParam("file") MultipartFile file) throws Exception {
        try {
            log.info("开始导入Excel文件，文件名: {}", file.getOriginalFilename());

            // 首先使用通用监听器来分析Excel结构
            GenericExcelListener genericListener = new GenericExcelListener();
            EasyExcel.read(file.getInputStream(), genericListener)
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet(0)
                    .headRowNumber(2) // 跳过前两行标题
                    .autoTrim(true)
                    .doRead();

            List<Map<Integer, String>> rawData = genericListener.getDataList();
            log.info("使用通用监听器读取到的数据行数: {}", rawData.size());

            List<AttendanceImportExcelVO> datas = new ArrayList<>();
            if (!rawData.isEmpty()) {
                Map<Integer, String> firstRow = rawData.get(0);
                log.info("第一行数据列数: {}", firstRow.size());

                // 转换为AttendanceImportExcelVO
                datas = convertRawDataToVO(rawData);
                log.info("转换后的数据行数: {}", datas.size());

            }

            return success(attendanceService.importAttendanceList(datas));
        } catch (Exception e) {
            log.error("Excel导入失败", e);
            return error(500, "Excel导入失败: " + e.getMessage());
        }
    }

    @GetMapping("/get-import-template")
    @Operation(summary = "获得员工考勤登记表导入模板")
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        List<AttendanceImportExcelVO> list = Arrays.asList(
                new AttendanceImportExcelVO()
                        .setEmployeeName("冯杰")
                        .setAttendanceGroup("研发弹性班制")
                        .setDepartment("研发部")
                        .setAttendanceDate("25-07-08 星期二")
                        .setShift("常规班 08:30-17:00")
                        .setWork1CheckinTime("08:28")
                        .setWork1CheckinResult("正常")
                        .setWork1CheckoutTime("17:30")
                        .setWork1CheckoutResult("正常"),
                new AttendanceImportExcelVO()
                        .setEmployeeName("胡蓉(离职)")
                        .setAttendanceGroup("研发弹性班制")
                        .setDepartment("研发部")
                        .setAttendanceDate("25-07-08 星期二")
                        .setShift("常规班 08:30-17:00")
                        .setWork1CheckinTime("08:30")
                        .setWork1CheckinResult("正常")
                        .setWork1CheckoutTime("17:00")
                        .setWork1CheckoutResult("正常")
                        .setWork2CheckoutTime("21:13")
                        .setWork2CheckoutResult("正常")
                        .setWork3CheckoutTime("外出07-08 08:30到07-08 12:45")
                        .setWork3CheckoutResult("0.5天")
        );
        ExcelUtils.write(response, "员工考勤登记表导入模板.xls", "员工考勤登记表", AttendanceImportExcelVO.class, list);
    }

    /**
     * 将原始数据转换为AttendanceImportExcelVO
     */
    private List<AttendanceImportExcelVO> convertRawDataToVO(List<Map<Integer, String>> rawData) {
        List<AttendanceImportExcelVO> result = new ArrayList<>();

        for (Map<Integer, String> row : rawData) {
            AttendanceImportExcelVO vo = new AttendanceImportExcelVO();

            // 根据列索引映射数据
            vo.setEmployeeName(getValueFromRow(row, 0));           // A列：姓名
            vo.setAttendanceGroup(getValueFromRow(row, 1));        // B列：考勤组
            vo.setDepartment(getValueFromRow(row, 2));             // C列：部门
            vo.setUserId(getValueFromRow(row, 3));                 // D列：UserId
            vo.setAttendanceDate(getValueFromRow(row, 4));         // E列：日期
            vo.setWorkDate(getValueFromRow(row, 5));               // F列：workDate
            vo.setShift(getValueFromRow(row, 6));                  // G列：班次
            vo.setWork1CheckinTime(getValueFromRow(row, 7));       // H列：上班1打卡时间
            vo.setWork1CheckinResult(getValueFromRow(row, 8));     // I列：上班1打卡结果
            vo.setWork1CheckoutTime(getValueFromRow(row, 9));      // J列：下班1打卡时间
            vo.setWork1CheckoutResult(getValueFromRow(row, 10));   // K列：下班1打卡结果
            vo.setWork2CheckinTime(getValueFromRow(row, 11));      // L列：上班2打卡时间
            vo.setWork2CheckinResult(getValueFromRow(row, 12));    // M列：上班2打卡结果
            vo.setWork2CheckoutTime(getValueFromRow(row, 13));     // N列：下班2打卡时间
            vo.setWork2CheckoutResult(getValueFromRow(row, 14));   // O列：下班2打卡结果
            vo.setWork3CheckinTime(getValueFromRow(row, 15));      // P列：上班3打卡时间
            vo.setWork3CheckinResult(getValueFromRow(row, 16));    // Q列：上班3打卡结果
            vo.setWork3CheckoutTime(getValueFromRow(row, 17));     // R列：下班3打卡时间
            vo.setWork3CheckoutResult(getValueFromRow(row, 18));   // S列：下班3打卡结果

            // 只有姓名不为空才添加到结果中
            if (vo.getEmployeeName() != null && !vo.getEmployeeName().trim().isEmpty()) {
                result.add(vo);
            }
        }

        return result;
    }

    /**
     * 从行数据中安全获取指定列的值
     */
    private String getValueFromRow(Map<Integer, String> row, int columnIndex) {
        String value = row.get(columnIndex);
        return (value != null && !value.trim().isEmpty()) ? value.trim() : null;
    }

}