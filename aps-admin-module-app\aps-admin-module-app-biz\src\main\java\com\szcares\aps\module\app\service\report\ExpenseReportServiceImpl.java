package com.szcares.aps.module.app.service.report;

import com.alibaba.excel.EasyExcel;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.annotations.VisibleForTesting;
import com.szcares.aps.module.app.controller.admin.quoto.vo.GetQuotaManagementDataResponse;
import com.szcares.aps.module.app.controller.admin.quoto.vo.PageResult;
import com.szcares.aps.module.app.controller.admin.report.vo.*;
import com.szcares.aps.module.app.dal.postgresql.report.ExpenseReportMapper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static com.szcares.aps.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.szcares.aps.module.system.enums.ErrorCodeConstants.*;

@Slf4j
@Service
public class ExpenseReportServiceImpl implements ExpenseReportService{

    @Resource
    ExpenseReportMapper expenseReportMapper;

    @Override
    @DS("apsds")
    public PageResult<GetQuotaManagementDataResponse> getAccountQuotaRanking(GetRankingDataCommonReq req){
        // 1.校验参数
        validateRequest(req);
        // 处理分页参数
        int pageSize = Optional.ofNullable(req.getPageSize()).orElse(10);
        int page = req.getPage() != null ? req.getPage() : 1;
        int offset = (page - 1) * pageSize;

        // 2. 查询数据与总数
        List<GetQuotaManagementDataResponse> responses = expenseReportMapper.findResult(req, offset);
        long total = expenseReportMapper.countResult(req);

        return new PageResult<>(responses, total, page, pageSize);

    }

    @Override
    @DS("apsds")
    public PageResult<GetAppQuotaRankingDataRes> getAppQuotaRankingData(GetRankingDataCommonReq req){
        // 1.校验参数
        validateRequest(req);
        // 处理分页参数
        int pageSize = Optional.ofNullable(req.getPageSize()).orElse(10);
        int page = req.getPage() != null ? req.getPage() : 1;
        int offset = (page - 1) * pageSize;

        // 2. 查询数据
        List<GetAppQuotaRankingDataRes> responses = expenseReportMapper.getAppQuotaRankingData(pageSize,offset);

        return new PageResult<>(responses, null, page, pageSize);
    }

    @Override
    @DS("apsds")
    public PageResult<GetAppTokenQuotaRankingDataRes> getAppTokenQuotaRankingData(GetRankingDataCommonReq req){
        // 1.校验参数
        validateRequest(req);
        // 处理分页参数
        int pageSize = Optional.ofNullable(req.getPageSize()).orElse(10);
        int page = req.getPage() != null ? req.getPage() : 1;
        int offset = (page - 1) * pageSize;

        //  2. 获取数据
        List<GetAppTokenQuotaRankingDataRes> responses = expenseReportMapper.getAppTokenQuotaRankingData(pageSize,offset);

        return new PageResult<>(responses, null, page, pageSize);

    }

    @Override
    @DS("apsds")
    public List<GetAppTokenDailyQuotaDataRes> getAppTokenDailyQuotaData(){
        // 1.校验参数
        //validateTokenDayilyRequest(req);  固定查询7天数据，不需要传入其他参数

        //  2. 获取数据
        List<GetAppTokenDailyQuotaDataRes> responses = expenseReportMapper.getAppTokenDailyQuotaData();

        return responses;
    }

    @Override
    @DS("apsds")
    public PageResult<GetAiImageQuotaRankingRes> getAiImageQuotaRankingData(GetAiImageQuotaRankingDataReq req){
        //校验参数
        validateAiImageQuotaRankingRequest(req);
        // 计算时间范围
        LocalDateTime startDate = req.getStatAt();
        LocalDateTime endDate = startDate != null ? startDate.plusMonths(1) : null;
        // 处理分页参数
        int pageSize = Optional.ofNullable(req.getPageSize()).orElse(10);
        int page = req.getPage() != null ? req.getPage() : 1;
        int offset = (page - 1) * pageSize;
        //获取数据
        List<GetAiImageQuotaRankingRes> responses = expenseReportMapper.getAiImageQuotaRankingData(startDate,endDate,pageSize,offset);

        return new PageResult<>(responses, null, page, pageSize);
    }

    @Override
    @DS("apsds")
    public List<MessageExportDTO> getMessagesForExport(String tenantName) {
        return expenseReportMapper.selectMessagesForExport(tenantName);
    }


    @Override
    @DS("apsds")
    public void exportToExcel(List<MessageExportDTO> messages, HttpServletResponse response) {
        try {
            String fileName = URLEncoder.encode("消息数据报表", StandardCharsets.UTF_8);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename*=" + fileName + ".xlsx");

            EasyExcel.write(response.getOutputStream(), MessageExportDTO.class)
                    .sheet("消息数据")
                    .doWrite(messages);
        } catch (IOException e) {
            throw new RuntimeException("导出Excel失败", e);
        }
    }


    @VisibleForTesting
    public void validateRequest(GetRankingDataCommonReq req) {
        if (req == null) {
            throw exception(QUOTA_PARAM_NOT_NULL);
        }

        if (req.getPage() != null && req.getPage() < 1) {
            throw exception(QUOTA_PAGE_NUM_GT_0);
        }

        if (req.getPageSize() != null && req.getPageSize() < 1) {
            throw exception(QUOTA_PAGE_SIZE_GT_0);
        }
    }

    @VisibleForTesting
    public void validateTokenDayilyRequest(GetAppTokenDailyQuotaDataReq req){

        if (req == null) {
            throw exception(QUOTA_PARAM_NOT_NULL);
        }

        if (req.getPage() != null && req.getPage() < 1) {
            throw exception(QUOTA_PAGE_NUM_GT_0);
        }

        if (req.getPageSize() != null && req.getPageSize() < 1) {
            throw exception(QUOTA_PAGE_SIZE_GT_0);
        }

        if (req.getAppId() == null){
            throw exception(QUOTA_REPORT_APP_ID_NOT_NULL);
        }
        if (req.getStatAt() == null) {
            throw exception(QUOTA_REPORT_STAT_AT_NOT_NULL);
        }
    }

    @VisibleForTesting
    public void validateAiImageQuotaRankingRequest(GetAiImageQuotaRankingDataReq req){
        if (req == null) {
            throw exception(QUOTA_PARAM_NOT_NULL);
        }

        if (req.getPage() != null && req.getPage() < 1) {
            throw exception(QUOTA_PAGE_NUM_GT_0);
        }

        if (req.getPageSize() != null && req.getPageSize() < 1) {
            throw exception(QUOTA_PAGE_SIZE_GT_0);
        }

        if (req.getStatAt() == null){
            throw exception(QUOTA_REPORT_STAT_AT_NOT_NULL);
        }
    }



}
