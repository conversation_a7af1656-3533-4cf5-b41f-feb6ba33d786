package com.szcares.aps.module.app.dal.postgresql.report;


import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.szcares.aps.module.app.controller.admin.quoto.vo.GetQuotaManagementDataResponse;
import com.szcares.aps.module.app.controller.admin.report.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface ExpenseReportMapper {

    List<GetAppQuotaRankingDataRes> getAppQuotaRankingData(
            @Param("pageSize") int pageSize,
            @Param("offset") int offset);

    List<GetAppTokenQuotaRankingDataRes> getAppTokenQuotaRankingData(
            @Param("pageSize") int pageSize,
            @Param("offset") int offset);

    @InterceptorIgnore(dataPermission = "true")
    List<GetAppTokenDailyQuotaDataRes> getAppTokenDailyQuotaData();

    List<GetAiImageQuotaRankingRes> getAiImageQuotaRankingData(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            @Param("pageSize") int pageSize,
            @Param("offset") int offset);

    List<MessageExportDTO> selectMessagesForExport(
            @Param("tenantName") String tenantName);

    List<GetQuotaManagementDataResponse> findResult(
            @Param("req") GetRankingDataCommonReq req,
            @Param("offset") int offset
    );

    Long countResult(@Param("req") GetRankingDataCommonReq req);
}
