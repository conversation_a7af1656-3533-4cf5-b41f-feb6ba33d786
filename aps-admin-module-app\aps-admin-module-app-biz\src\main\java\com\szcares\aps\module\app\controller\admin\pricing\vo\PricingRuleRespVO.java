package com.szcares.aps.module.app.controller.admin.pricing.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class PricingRuleRespVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "供应商")
    private String provider;

    @Schema(description = "模型名称")
    private String modelName;

    @Schema(description = "使用模式")
    private String usageMode;

    @Schema(description = "输入token价格/千token")
    private BigDecimal inputPrice;

    @Schema(description = "输出token价格/千token")
    private BigDecimal outputPrice;

    @Schema(description = "图像token价格/千token")
    private BigDecimal imagePrice;

    @Schema(description = "加价倍率")
    private BigDecimal markupRate;

    @Schema(description = "计价货币")
    private String currency;

    @Schema(description = "生效时间")
    private LocalDateTime effectiveDate;

    @Schema(description = "失效时间")
    private LocalDateTime expiryDate;

    @Schema(description = "创建人")
    private String createdBy;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "状态")
    private Integer status;
}