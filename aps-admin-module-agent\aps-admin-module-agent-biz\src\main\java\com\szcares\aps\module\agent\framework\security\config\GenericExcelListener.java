package com.szcares.aps.module.agent.framework.security.config;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 通用Excel导入监听器
 * 用于读取任意格式的Excel文件，不依赖具体的列索引
 *
 * <AUTHOR>
 */
@Slf4j
public class GenericExcelListener implements ReadListener<Map<Integer, String>> {

    /**
     * 数据列表
     */
    private final List<Map<Integer, String>> dataList = new ArrayList<>();

    /**
     * 当前行号
     */
    private int currentRow = 0;

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        currentRow++;
        log.info("正在读取第{}行数据", currentRow);
        
        if (data != null && !data.isEmpty()) {
            // 打印每一列的数据
            StringBuilder sb = new StringBuilder();
            sb.append("第").append(currentRow).append("行数据: ");
            for (Map.Entry<Integer, String> entry : data.entrySet()) {
                sb.append("列").append(entry.getKey()).append("=").append(entry.getValue()).append(", ");
            }
            log.info(sb.toString());
            
            dataList.add(data);
        } else {
            log.info("第{}行数据为空", currentRow);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("Excel读取完成，共读取{}行有效数据", dataList.size());
    }

    /**
     * 获取读取到的数据列表
     */
    public List<Map<Integer, String>> getDataList() {
        return dataList;
    }
}
