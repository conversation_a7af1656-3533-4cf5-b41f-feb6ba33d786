package com.szcares.aps.module.app.controller.admin.report.vo;


import lombok.Data;
import org.dromara.hutool.core.lang.page.PageInfo;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
public class GetAppTokenDailyQuotaDataReq {

    private Integer page;          // 页码
    private Integer pageSize;      // 每页大小
    private String keyword;    // 关键字
    private String appId;      // 应用ID
    private LocalDateTime statAt; // 统计时间

}
