package com.szcares.aps.module.agent.dal.dataobject.entryexit;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.szcares.aps.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 员工入职离职登记表 DO
 *
 * <AUTHOR>
 */
@TableName("employee_movements")
@KeySequence("employee_movements_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EntryExitDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 员工姓名
     */
    private String employeeName;
    /**
     * 变动类型
     */
    private String movementType;
    /**
     * 变动日期
     */
    private String movementDate;
    /**
     * 年份
     */
    private Integer year;

}