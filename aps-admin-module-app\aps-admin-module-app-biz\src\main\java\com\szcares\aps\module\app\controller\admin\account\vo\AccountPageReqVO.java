package com.szcares.aps.module.app.controller.admin.account.vo;

import com.szcares.aps.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 用户账号列表 Request VO")
@Data
public class AccountPageReqVO extends PageParam {

    @Schema(description = "用户账号名称，模糊匹配")
    private String name;

    @Schema(description = "用户账号名称，模糊匹配")
    private String email;

    @Schema(description = "状态")
    private String status;

}
