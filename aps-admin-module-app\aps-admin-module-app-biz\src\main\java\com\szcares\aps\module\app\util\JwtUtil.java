package com.szcares.aps.module.app.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;

import java.security.KeyFactory; // 添加导入
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

public class JwtUtil {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static String cleanKey(String key) {
        // 移除 PEM 格式的头部和尾部标记
        key = key.replaceAll("-----BEGIN (.*)-----", "");
        key = key.replaceAll("-----END (.*)-----", "");
        // 移除所有空白字符（包括换行符、空格等）
        key = key.replaceAll("\\s", "");
        return key.trim();
    }

    // 加密：入参改为 Object（支持 JSON 对象）
    public static String encryptWithRsa(Object jsonData, String privateKeyBase64) throws Exception {
        // 验证私钥是否为有效的 Base64 字符串
        if (!isValidBase64(privateKeyBase64)) {
            throw new IllegalArgumentException("Invalid Base64 format for private key");
        }
        // 将对象转换为Map
        Map<String, Object> claimsMap = convertToMap(jsonData);

        PrivateKey privateKey = generatePrivateKey(privateKeyBase64);

        return Jwts.builder()
                .setClaims(claimsMap) // 直接设置Claims
                .signWith(privateKey, SignatureAlgorithm.RS256)
                .compact();
    }

    // 解密：出参转回 Object（JSON 类型）
    public static Map<String, Object> decryptWithRsa(String jwtToken, String publicKeyBase64) throws Exception {
        String cleanedPublicKey = cleanKey(publicKeyBase64);
        if (!isValidBase64(cleanedPublicKey)) {
            throw new IllegalArgumentException("Invalid Base64 format for public key");
        }
        PublicKey publicKey = generatePublicKey(cleanedPublicKey);

        Claims claims = Jwts.parserBuilder()
                .setSigningKey(publicKey)
                .build()
                .parseClaimsJws(jwtToken)
                .getBody();

        // 将Claims转换为Map
        return new HashMap<>(claims);
    }

    private static Map<String, Object> convertToMap(Object object) throws Exception {
        if (object == null) {
            return new HashMap<>();
        }
        if (object instanceof Map) {
            return (Map<String, Object>) object;
        }
        // 使用ObjectMapper将对象转换为Map
        return objectMapper.convertValue(object, Map.class);
    }

    private static boolean isValidBase64(String str) {
        try {
            // 简单验证：检查字符串长度是否为 4 的倍数，并且只包含合法的 Base64 字符
            if (str == null || str.isEmpty()) {
                return false;
            }
            // 移除可能的填充字符
            str = str.replace("=", "");
            return str.matches("^[A-Za-z0-9+/]+$") && str.length() % 4 == 0;
        } catch (Exception e) {
            return false;
        }
    }

    private static PublicKey generatePublicKey(String publicKeyBase64) throws Exception {
        byte[] publicKeyBytes = Base64.getDecoder().decode(publicKeyBase64);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA"); // 使用 KeyFactory
        return keyFactory.generatePublic(keySpec);
    }

    private static PrivateKey generatePrivateKey(String privateKeyBase64) throws Exception {
        byte[] privateKeyBytes = Base64.getDecoder().decode(privateKeyBase64);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA"); // 使用 KeyFactory
        return keyFactory.generatePrivate(keySpec);
    }
}