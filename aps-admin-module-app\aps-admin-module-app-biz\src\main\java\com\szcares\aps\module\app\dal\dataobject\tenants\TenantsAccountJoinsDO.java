package com.szcares.aps.module.app.dal.dataobject.tenants;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 部门表
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@TableName("tenant_account_joins")
@Data
public class TenantsAccountJoinsDO implements Serializable, TransPojo {

    public static final Long PARENT_ID_ROOT = 0L;

    private String id;
    private String tenantId;
    private String accountId;
    private String role;
    private String invitedBy;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private boolean current;

}
