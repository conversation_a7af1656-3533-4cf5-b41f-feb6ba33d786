package com.szcares.aps.module.app.controller.admin.tenants.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "管理后台 - 租户精简信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TenantsSimpleRespVO {

    @Schema(description = "租户编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;

    @Schema(description = "租户名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

}
