package com.szcares.aps.module.app.controller.admin.account.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Schema(description = "管理后台 - 用户账号创建/修改 Request VO")
@Data
public class AccountSaveReqVO {

    @Schema(description = "id")
    private String id;
    @Schema(description = "昵称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "用户昵称不能为空")
    private String name;
    @Schema(description = "邮箱", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "邮箱不能为空")
    private String email;
    private String password;
    private String passwordSalt;
    private String avatar;
    private String interfaceLanguage;
    private String interfaceTheme;
    private String timezone;
    private String status;

}
