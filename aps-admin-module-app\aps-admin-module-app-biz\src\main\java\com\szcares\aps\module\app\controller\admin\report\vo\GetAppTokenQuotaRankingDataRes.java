package com.szcares.aps.module.app.controller.admin.report.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "管理后台 - 获取应用密钥配额排名数据的响应 Response VO")
public class GetAppTokenQuotaRankingDataRes {

    private int ranking;              // 排名
    private String name;              // 对应应用名称
    private String appToken;          // 密钥（需要加密显示）
    private double accumulatedQuota;  // 累计使用
    private double dayUsedQuota;      // 日使用
    private double monthUsedQuota;    // 月使用
    private double dayLimitQuota;     // 日限额
    private double monthLimitQuota;   // 月限额
}
