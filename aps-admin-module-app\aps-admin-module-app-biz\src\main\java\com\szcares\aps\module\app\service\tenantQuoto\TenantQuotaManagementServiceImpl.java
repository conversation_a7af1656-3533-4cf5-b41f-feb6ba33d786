package com.szcares.aps.module.app.service.tenantQuoto;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.annotations.VisibleForTesting;
import com.szcares.aps.module.app.controller.admin.tenantQuoto.vo.GetTenantQuotaManagementDataResponse;
import com.szcares.aps.module.app.controller.admin.tenantQuoto.vo.GetTenantQuotaRankingDataReq;
import com.szcares.aps.module.app.controller.admin.tenantQuoto.vo.PageResult;
import com.szcares.aps.module.app.controller.admin.tenantQuoto.vo.SetTenantQuotaReq;
import com.szcares.aps.module.app.dal.dataobject.tenantQuoto.TenantQuotaDO;
import com.szcares.aps.module.app.dal.postgresql.tenantQuoto.TenantQuotaManagementMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static com.szcares.aps.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.szcares.aps.module.system.enums.ErrorCodeConstants.*;

@Slf4j
@Service
public class TenantQuotaManagementServiceImpl implements TenantQuotaManagementService {

    @Resource
    private TenantQuotaManagementMapper tenantQuotaManagementMapper;

    @Override
    @DS("apsds")
    public void setTenantQuota(SetTenantQuotaReq req) {
        // 校验自己存在
        validateQuotoExists(req.getId());
        // 构建更新对象
        TenantQuotaDO quotoDO = new TenantQuotaDO();
        quotoDO.setTenantId(req.getId());
        quotoDO.setTotalQuota(req.getQuota());
        //更新时间
        quotoDO.setUpdatedAt(LocalDateTime.now());
        //更新
        try {
            tenantQuotaManagementMapper.updateById(quotoDO);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    @DS("apsds")
    public PageResult<GetTenantQuotaManagementDataResponse> getTenantQuotaManagementList(GetTenantQuotaRankingDataReq req){
        // 1.校验参数
        validateRequest(req);
        // 处理分页参数
        int pageSize = Optional.ofNullable(req.getPageSize()).orElse(10);
        int page = req.getPage() != null ? req.getPage() : 1;
        int offset = (page - 1) * pageSize;

        // 2. 查询数据与总数
        List<GetTenantQuotaManagementDataResponse> responses = tenantQuotaManagementMapper.findResult(req, offset);
        long total = tenantQuotaManagementMapper.countResult(req);
        return new PageResult<>(responses, total, page, pageSize);
    }

    @VisibleForTesting
    @DS("apsds")
    public void validateQuotoExists(String uuid){
        if (uuid == null){
            return;
        }
        UUID uid = UUID.fromString(uuid);
        TenantQuotaDO quotoDO = tenantQuotaManagementMapper.selectById(uuid);
        if (quotoDO == null){
            throw exception(QUOTA_NOT_EXISTS);
        }
    }

    @VisibleForTesting
    private void validateRequest(GetTenantQuotaRankingDataReq req) {
        if (req == null) {
            throw exception(QUOTA_PARAM_NOT_NULL);
        }

        if (req.getPage() != null && req.getPage() < 1) {
            throw exception(QUOTA_PAGE_NUM_GT_0);
        }

        if (req.getPageSize() != null && req.getPageSize() < 1) {
            throw exception(QUOTA_PAGE_SIZE_GT_0);
        }
    }

}
