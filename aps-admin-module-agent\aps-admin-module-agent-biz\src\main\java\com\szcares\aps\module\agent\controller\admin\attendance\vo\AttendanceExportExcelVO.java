package com.szcares.aps.module.agent.controller.admin.attendance.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 员工考勤登记表 Excel 导出 VO
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
public class AttendanceExportExcelVO {

    @ExcelProperty("主键ID")
    private Long id;

    @ExcelProperty("员工姓名")
    private String employeeName;

    @ExcelProperty("考勤组")
    private String attendanceGroup;

    @ExcelProperty("所属部门")
    private String department;

    @ExcelProperty("用户ID")
    private String userId;

    @ExcelProperty("考勤日期")
    private String attendanceDate;

    @ExcelProperty("工作日期相关信息")
    private String workDate;

    @ExcelProperty("班次")
    private String shift;

    @ExcelProperty("第一次上班打卡时间")
    private String work1CheckinTime;

    @ExcelProperty("第一次上班打卡结果")
    private String work1CheckinResult;

    @ExcelProperty("第一次下班打卡时间")
    private String work1CheckoutTime;

    @ExcelProperty("第一次下班打卡结果")
    private String work1CheckoutResult;

    @ExcelProperty("关联的审批单")
    private String relatedApprovalForm;

    @ExcelProperty("出差时长")
    private String businessTripDuration;

    @ExcelProperty("外出时长")
    private String outingDuration;

    @ExcelProperty("病假天数")
    private String sickLeaveDays;

    @ExcelProperty("年假天数")
    private String annualLeaveDays;

    @ExcelProperty("事假天数")
    private String personalLeaveDays;

    @ExcelProperty("丧假天数")
    private String mourningLeaveDays;

    @ExcelProperty("调休天数")
    private String compensatoryLeaveDays;

    @ExcelProperty("探亲假天数")
    private String homeVisitLeaveDays;

    @ExcelProperty("产假天数")
    private String maternityLeaveDays;

    @ExcelProperty("陪产假天数")
    private String paternityLeaveDays;

    @ExcelProperty("婚假天数")
    private String marriageLeaveDays;

    @ExcelProperty("哺乳假天数")
    private String nursingLeaveDays;

    @ExcelProperty("育儿假天数")
    private String childcareLeaveDays;

    @ExcelProperty("护理假天数")
    private String careLeaveDays;

    @ExcelProperty("例假天数")
    private String menstrualLeaveDays;

    @ExcelProperty("加班总时长")
    private String totalOvertimeDuration;

    @ExcelProperty("工作日（转调休）")
    private String workingDayCompensatoryLeave;

    @ExcelProperty("休息日（转调休）")
    private String restDayCompensatoryLeave;

    @ExcelProperty("节假日（转调休）")
    private String holidayCompensatoryLeave;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}