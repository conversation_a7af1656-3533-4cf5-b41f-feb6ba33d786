package com.szcares.aps.module.app.dal.postgresql.account;

import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.framework.mybatis.core.mapper.BaseMapperX;
import com.szcares.aps.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.szcares.aps.module.app.controller.admin.account.vo.AccountListByTenantReqVO;
import com.szcares.aps.module.app.controller.admin.account.vo.AccountListReqVO;
import com.szcares.aps.module.app.controller.admin.account.vo.AccountPageReqVO;
import com.szcares.aps.module.app.controller.admin.tenants.vo.TenantsListReqVO;
import com.szcares.aps.module.app.dal.dataobject.account.AccountDO;
import com.szcares.aps.module.app.dal.dataobject.tenants.TenantsDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface AccountMapper extends BaseMapperX<AccountDO> {

    default List<AccountDO> selectList(AccountListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AccountDO>()
                .likeIfPresent(AccountDO::getName, reqVO.getName())
                .eqIfPresent(AccountDO::getStatus, reqVO.getStatus())
                .eqIfPresent(AccountDO::getEmail, reqVO.getEmail()));
    }

    @Select("SELECT taj.id as id, a.name as name, a.email as email, taj.role FROM accounts a "+
            "INNER JOIN tenant_account_joins taj ON taj.account_id = a.id "+
            "INNER JOIN tenants t ON t.id = taj.tenant_id "+
            "WHERE t.id = #{entity.tenantId}::uuid "+
            "ORDER BY a.created_at DESC "
    )
    List<AccountDO> getSimpleAccountListByTenant(@Param("entity") AccountListByTenantReqVO reqVO);

    default PageResult<AccountDO> selectPage(AccountPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AccountDO>()
                .likeIfPresent(AccountDO::getName, reqVO.getName())
                .likeIfPresent(AccountDO::getEmail, reqVO.getEmail())
                .eqIfPresent(AccountDO::getStatus, reqVO.getStatus())
                .orderByDesc(AccountDO::getEmail));
    }

    default AccountDO selectByEmail(String email) {
        return selectOne(AccountDO::getEmail, email);
    }

    @Select("SELECT * FROM accounts WHERE id = #{id}::uuid")
    AccountDO selectById(String id);


    @Insert("INSERT INTO public.accounts " +
        "(id, name, email, password, password_salt, avatar, interface_language, interface_theme, " +
        "timezone, status, created_at, updated_at) " +
        "VALUES (" +
        "#{entity.id}::uuid, " +
        "#{entity.name}, " +
        "#{entity.email}, " +
        "#{entity.password}, " +
        "#{entity.passwordSalt}, " +
        "#{entity.avatar}, " +
        "#{entity.interfaceLanguage}, " +
        "#{entity.interfaceTheme}, " +
        "#{entity.timezone}, " +
        "#{entity.status}, " +
        "#{entity.createdAt}, " +
        "#{entity.updatedAt}" +
        ")")
    int insertByAccountDO(@Param("entity") AccountDO entity);


    @Update("UPDATE public.accounts SET " +
        "name = #{entity.name}, " +
        "status = #{entity.status}, " +
        "updated_at = #{entity.updatedAt} " +  // 不更新 createdAt
        "WHERE id = #{entity.id}::uuid")
    int updateByAccountDO(@Param("entity") AccountDO entity);

    @Update("UPDATE public.accounts SET " +
            "password = #{entity.password}, " +
            "password_salt = #{entity.passwordSalt}, " +
            "updated_at = #{entity.updatedAt} " +  // 不更新 createdAt
            "WHERE id = #{entity.id}::uuid")
    int updateByPWD(@Param("entity") AccountDO entity);

    @Delete("DELETE FROM accounts WHERE id = #{id}::uuid")
    int deleteByAccountId(String id);
}
