package com.szcares.aps.module.app.service.pricing.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.module.app.controller.admin.pricing.vo.PricingRuleLogPageReqVO;
import com.szcares.aps.module.app.dal.dataobject.pricing.PricingRuleLogDO;
import com.szcares.aps.module.app.dal.postgresql.pricing.PricingRuleLogMapper;
import com.szcares.aps.module.app.service.pricing.PricingRuleLogsService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class PricingRuleLogsServiceImpl implements PricingRuleLogsService {


    @Resource
    private PricingRuleLogMapper pricingRuleLogMapper;

    @Override
    @DS("apsds")
    public PageResult<PricingRuleLogDO> getLogPage(PricingRuleLogPageReqVO reqVO) {
        return pricingRuleLogMapper.selectPage(reqVO);
    }

}