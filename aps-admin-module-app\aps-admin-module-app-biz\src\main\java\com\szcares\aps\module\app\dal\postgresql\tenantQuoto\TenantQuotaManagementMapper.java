package com.szcares.aps.module.app.dal.postgresql.tenantQuoto;

import com.szcares.aps.framework.mybatis.core.mapper.BaseMapperX;
import com.szcares.aps.module.app.controller.admin.tenantQuoto.vo.GetTenantQuotaManagementDataResponse;
import com.szcares.aps.module.app.controller.admin.tenantQuoto.vo.GetTenantQuotaRankingDataReq;
import com.szcares.aps.module.app.dal.dataobject.tenantQuoto.TenantQuotaDO;
import com.szcares.aps.module.app.dal.dataobject.tenants.TenantsDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface TenantQuotaManagementMapper extends BaseMapperX<TenantQuotaDO> {

    @Select("SELECT * FROM tenant_money_extend WHERE tenant_id = CAST(#{id} AS uuid)")
    TenantQuotaDO selectById(@Param("id") String id);

    @Update("UPDATE public.tenant_money_extend SET " +
            "total_quota = #{entity.totalQuota}, " +
            "updated_at = NOW() " +
            "WHERE tenant_id = #{entity.tenantId}::uuid")
    int updateById(@Param("entity") TenantQuotaDO entity);

    @Insert("insert INTO public.tenant_money_extend " +
            "( id,tenant_id,total_quota,used_quota,created_at,updated_at)" +
            "VALUES( #{entity.id}::uuid, #{entity.tenantId}::uuid, #{entity.totalQuota}, #{entity.usedQuota}, NOW(), NOW() )" )
    int insert(@Param("entity") TenantQuotaDO entity);

    @Select({
            "<script>",
            "SELECT",
            "   ame.tenant_id,",
            "   RANK() OVER (ORDER BY ame.used_quota DESC) AS ranking,",
            "   a.name AS tenant_name,",
            "   ame.used_quota,",
            "   ame.total_quota",
            "FROM public.tenant_money_extend ame",
            "JOIN tenants a ON ame.tenant_id = a.id",
            "<where>",
            "   <if test='req.keyword != null and req.keyword.trim() != \"\"'>",
            "       <bind name='likeKeyword' value=\"'%' + keyword + '%'\" />",
            "       a.name LIKE #{likeKeyword} OR a.email LIKE #{likeKeyword}",
            "   </if>",
            "</where>",
            "ORDER BY ranking",
            "LIMIT #{req.pageSize} OFFSET #{offset}",
            "</script>"
    })
    List<GetTenantQuotaManagementDataResponse> findResult(@Param("req") GetTenantQuotaRankingDataReq req
    , @Param("offset") int offset);


    @Select({
            "<script>",
            "SELECT COUNT(*) FROM public.tenant_money_extend ame JOIN tenants a ON ame.tenant_id = a.id WHERE 1=1",
            "<if test='req.keyword != null and req.keyword != \"\"'>",
            "AND (a.name ILIKE '%' || #{req.keyword} || '%' OR a.email ILIKE '%' || #{req.keyword} || '%')",
            "</if>",
            "</script>"
    })
    Long countResult(@Param("req") GetTenantQuotaRankingDataReq req);

}

