package com.szcares.aps.module.app.controller.admin.account.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Schema(description = "管理后台 - 用户账号信息 Response VO")
@Data
public class AccountRespVO {

    private String id;
    @Schema(description = "用户名称")
    private String name;
    @Schema(description = "邮箱")
    private String email;
    @Schema(description = "时区")
    private String timezone;
    @Schema(description = "最后登录时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "yyyy-mm-dd")
    private Date lastLoginAt;
    @Schema(description = "最后登录IP")
    private String lastLoginIp;
    @Schema(description = "状态")
    private String status;
    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "yyyy-mm-dd")
    private Date initializedAt;
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "yyyy-mm-dd")
    private Date createdAt;
    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "yyyy-mm-dd")
    private Date updatedAt;
    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "yyyy-mm-dd")
    private Date lastActiveAt;
    @Schema(description = "头像")
    private String avatar;
    @Schema(description = "语言")
    private String interfaceLanguage;
    @Schema(description = "主题")
    private String interfaceTheme;
    @Schema(description = "角色")
    private String role;
    
}
