package com.szcares.aps.module.app.util;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 工具类：用于比较两个对象的字段差异
 */
public class BeanDiffUtil {

    /**
     * 比较两个对象的字段差异，返回差异字段及其前后值
     * @param oldObj 修改前对象
     * @param newObj 修改后对象
     * @return 差异字段map，key为字段名，value为"oldValue -> newValue"
     */
    public static Map<String, String> diff(Object oldObj, Object newObj) {
        Map<String, String> diffMap = new HashMap<>();
        if (oldObj == null || newObj == null) {
            return diffMap;
        }
        Class<?> clazz = oldObj.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                Object oldValue = field.get(oldObj);
                Object newValue = field.get(newObj);
                if (!Objects.equals(oldValue, newValue)) {
                    diffMap.put(field.getName(),
                            String.valueOf(oldValue) + " -> " + String.valueOf(newValue));
                }
            } catch (IllegalAccessException e) {
                // 可根据需要记录日志
            }
        }
        return diffMap;
    }
}
