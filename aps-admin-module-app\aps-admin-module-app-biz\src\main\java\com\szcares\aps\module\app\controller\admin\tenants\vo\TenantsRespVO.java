package com.szcares.aps.module.app.controller.admin.tenants.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.UUID;

@Schema(description = "管理后台 - 租户信息 Response VO")
@Data
public class TenantsRespVO {

    @Schema(description = "租户编号")
    private String id;

    @Schema(description = "租户名称")
    private String name;

    private String encryptPublicKey;

    private String plan;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "创建时间")
    private String createdAt;

    @Schema(description = "更新时间")
    private String updatedAt;

    private String customConfig;

}
