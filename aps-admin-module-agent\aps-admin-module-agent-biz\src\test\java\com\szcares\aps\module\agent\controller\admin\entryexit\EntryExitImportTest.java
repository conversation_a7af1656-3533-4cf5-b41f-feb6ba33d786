package com.szcares.aps.module.agent.controller.admin.entryexit;

import com.szcares.aps.module.agent.controller.admin.entryexit.vo.EntryExitImportExcelVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * 员工入职离职导入测试
 */
@Slf4j
public class EntryExitImportTest {

    @Test
    public void testSheetNameParsing() {
        // 测试sheet名称解析逻辑
        String[] sheetNames = {"2025入职", "2025离职", "2024入职", "2024离职"};
        
        for (String sheetName : sheetNames) {
            String movementType = null;
            Integer year = null;
            
            if (sheetName.contains("入职")) {
                movementType = "入职";
            } else if (sheetName.contains("离职")) {
                movementType = "离职";
            }
            
            // 从sheet名称中提取年份
            try {
                String yearStr = sheetName.replaceAll("[^0-9]", "");
                if (!yearStr.isEmpty()) {
                    year = Integer.parseInt(yearStr);
                }
            } catch (NumberFormatException e) {
                log.warn("无法从sheet名称 '{}' 中解析年份", sheetName);
            }
            
            log.info("Sheet '{}' 解析结果: movementType={}, year={}", sheetName, movementType, year);
        }
    }

    @Test
    public void testDataValidation() {
        // 测试数据验证逻辑
        List<EntryExitImportExcelVO> testData = new ArrayList<>();
        
        // 有效数据
        EntryExitImportExcelVO valid = new EntryExitImportExcelVO();
        valid.setEmployeeName("张三");
        valid.setMovementDate("2025-01-01");
        valid.setMovementType("入职");
        valid.setYear(2025);
        testData.add(valid);
        
        // 姓名为空
        EntryExitImportExcelVO emptyName = new EntryExitImportExcelVO();
        emptyName.setEmployeeName("");
        emptyName.setMovementDate("2025-01-01");
        testData.add(emptyName);
        
        // 日期为空
        EntryExitImportExcelVO emptyDate = new EntryExitImportExcelVO();
        emptyDate.setEmployeeName("李四");
        emptyDate.setMovementDate("");
        testData.add(emptyDate);
        
        // 测试验证逻辑
        for (int i = 0; i < testData.size(); i++) {
            EntryExitImportExcelVO data = testData.get(i);
            boolean isValid = data.getEmployeeName() != null && !data.getEmployeeName().trim().isEmpty()
                    && data.getMovementDate() != null && !data.getMovementDate().trim().isEmpty();
            
            log.info("第{}条数据验证结果: 姓名={}, 日期={}, 有效={}", 
                    i + 1, data.getEmployeeName(), data.getMovementDate(), isValid);
        }
    }
}
