package com.szcares.aps.module.app.service.license.impl;

/**
 * <AUTHOR>
 * @date 2025/6/11 11:16
 */
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.szcares.aps.module.app.controller.admin.license.vo.AgentLicenseReq;
import com.szcares.aps.module.app.dal.dataobject.license.AgentLicenseDO;
import com.szcares.aps.module.app.dal.mysql.license.AgentLicenseMapper;
import com.szcares.aps.module.app.service.license.AgentLicenseService;
import com.szcares.aps.module.app.util.JwtUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

@Service
@Slf4j
public class AgentLicenseServiceImpl implements AgentLicenseService {

    @Value("${jwt.privateKey}")
    private String privateKey;
    @Value("${jwt.publicKey}")
    private String publicKey;
    @Resource
    AgentLicenseMapper agentLicenseMapper;

    @Override
    public String generateLicense(AgentLicenseReq req) throws Exception {
        // 生成授权码
        String  encryptedJson = JwtUtil.encryptWithRsa(req.toString(),privateKey);
        log.info("生成的授权码为: {}", encryptedJson);
        AgentLicenseDO license = new AgentLicenseDO();
        license.setAgentName(req.getAgentName());
        license.setAgentId(req.getAgentId());
        license.setLicenseCode(encryptedJson);
        license.setStatus(0); // 未使用
        String expiredAt = req.getExpireTime();
        DateTimeFormatter formatter = DateTimeFormatter.ISO_DATE_TIME;
        LocalDateTime expiredDateTime = LocalDateTime.parse(expiredAt, formatter);
        license.setExpireTime(expiredDateTime);
        license.setCreateTime(LocalDateTime.now());
        license.setOperator(req.getOperator());

        agentLicenseMapper.insert(license);
        return encryptedJson;
    }

    @Override
    public List<AgentLicenseDO> listLicensesByAgentId(String agentId) {
        return agentLicenseMapper.selectList(new LambdaQueryWrapper<AgentLicenseDO>()
                .eq(StringUtil.isNotBlank(agentId),AgentLicenseDO::getAgentId, agentId)
                .orderByDesc(AgentLicenseDO::getCreateTime));
    }

    @Override
    public int updateExpireTime(Long id, String newExpireTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ISO_DATE_TIME;
        LocalDateTime expiredDateTime = LocalDateTime.parse(newExpireTime, formatter);
        AgentLicenseDO license = agentLicenseMapper.selectById(id);
        if (license == null || license.getStatus() != 0
                || license.getExpireTime().isBefore(LocalDateTime.now())) {
            log.info("授权码非未使用或已过期，无法修改: {}");
            return 0; // 非未使用或已过期，无法修改
        }
        license.setExpireTime(expiredDateTime);
        return agentLicenseMapper.updateById(license);
    }

    @Override
    public int markAsUsed(String licenseCode, String userId, String operator) {
        AgentLicenseDO license = agentLicenseMapper.selectOne(new LambdaQueryWrapper<AgentLicenseDO>()
                .eq(AgentLicenseDO::getLicenseCode, licenseCode)
                .eq(AgentLicenseDO::getStatus, 0)); // 仅未使用可标记

        if (license == null) {
            return 0;
        }

        license.setStatus(1);
        license.setUserId(userId);
        license.setUseTime(LocalDateTime.now());
        license.setOperator(operator);
        return agentLicenseMapper.updateById(license);
    }
}
