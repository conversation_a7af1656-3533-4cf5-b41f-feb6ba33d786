package com.szcares.aps.module.agent.controller.admin.attendance.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 员工考勤登记表导入 Response VO")
@Data
public class AttendanceImportRespVO {

    @Schema(description = "创建成功的条数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer createSuccessCount;

    @Schema(description = "更新成功的条数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer updateSuccessCount;

    @Schema(description = "导入失败的条数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer failureCount;

    @Schema(description = "失败的明细", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> failureMessages;

}