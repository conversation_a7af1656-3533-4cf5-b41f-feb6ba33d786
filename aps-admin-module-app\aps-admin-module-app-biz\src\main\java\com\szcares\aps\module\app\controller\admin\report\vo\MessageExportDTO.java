package com.szcares.aps.module.app.controller.admin.report.vo;


import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Data
public class MessageExportDTO {
    // 应用ID
    private String appId;

    // 名称（根据来源不同可能是账户名或租户名）
    private String name;

    // 租户名称
    private String tenantName;

    // 应用名称
    private String appName;

    // 模型提供商
    private String modelProvider;

    // 模型ID
    private String modelId;

    // 消息token数
    private Integer messageTokens;

    // 消息单价
    private BigDecimal messageUnitPrice;

    // 回答token数
    private Integer answerTokens;

    // 回答单价
    private BigDecimal answerUnitPrice;

    // 总价
    private BigDecimal totalPrice;

    // 货币类型
    private String currency;

    // 来源（api或其他）
    private String fromSource;

    // 终端用户ID
    private String fromEndUserId;

    // 账户ID
    private String fromAccountId;

    // 创建时间
    private LocalDateTime createdAt;

    // 工作流运行ID
    private String workflowRunId;

    // 状态
    private String status;

    // 调用来源
    private String invokeFrom;

}
