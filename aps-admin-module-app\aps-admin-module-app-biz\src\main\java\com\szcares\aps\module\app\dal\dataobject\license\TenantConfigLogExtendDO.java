package com.szcares.aps.module.app.dal.dataobject.license;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.szcares.aps.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 租户授权码日志表 DO
 *
 * <AUTHOR>
 */
@Data
@TableName("tenant_config_log_extend")
public class TenantConfigLogExtendDO extends BaseDO {

    /**
     * 租户授权码日志主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 部署租户的id
     */
    private String tenantsId;

    /**
     * 授权数据加密签名
     */
    private String jwtToken;

    /**
     * 请求ip
     */
    private String ip;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
