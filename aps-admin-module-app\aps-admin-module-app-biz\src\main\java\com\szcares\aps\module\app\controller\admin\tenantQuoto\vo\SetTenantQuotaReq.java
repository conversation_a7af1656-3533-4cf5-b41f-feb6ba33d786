package com.szcares.aps.module.app.controller.admin.tenantQuoto.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 设置额度")
@Data
public class SetTenantQuotaReq {

    @Schema(description = "租户id")
    @NotNull(message = "租户id不能为空")
    @Pattern(regexp = "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$",
            message = "ID必须是合法的UUID")
    private String id;

    @NotNull(message = "额度不能为空")
    @Schema(description = "额度")
    private BigDecimal quota;
}
