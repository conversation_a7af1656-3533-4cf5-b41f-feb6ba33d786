package com.szcares.aps.module.app.controller.admin.license.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/6/11 14:05
 */
@Schema(description = "智能体授权管理")
@Data
public class AgentLicenseReq {


    @NotNull(message = "智能体名字不能为空")
    @Schema(description = "智能体名字")
    @JsonProperty("agentName")
    private String agentName;

    @NotNull(message = "智能体不能为空")
    @Schema(description = "智能体id")
    @JsonProperty("agentId")
    private String agentId;

    @NotNull(message = "有效期不能为空")
    @Schema(description = "有效期")
    @JsonProperty("expireTime")
    private String expireTime;

    @Schema(description = "使用者")
    @JsonProperty("operator")
    private String operator;
}
