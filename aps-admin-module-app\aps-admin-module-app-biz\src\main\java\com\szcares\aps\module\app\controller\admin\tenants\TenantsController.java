package com.szcares.aps.module.app.controller.admin.tenants;

import com.szcares.aps.framework.common.enums.CommonStatusEnum;
import com.szcares.aps.framework.common.pojo.CommonResult;
import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.framework.common.util.object.BeanUtils;
import com.szcares.aps.module.app.controller.admin.tenants.vo.*;
import com.szcares.aps.module.app.convert.tenants.TenantsConvert;
import com.szcares.aps.module.app.dal.dataobject.tenants.TenantsDO;
import com.szcares.aps.module.app.service.tenants.TenantsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

import static com.szcares.aps.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 租户")
@RestController
@RequestMapping("/app/tenants")
@Validated
public class TenantsController {

    @Resource
    private TenantsService tenantsService;

    @PostMapping("create")
    @Operation(summary = "创建租户")
    //@PreAuthorize("@ss.hasPermission('system:tenants:create')")
    public CommonResult<String> createTenants(@Valid @RequestBody TenantsSaveReqVO createReqVO) {
        String TenantsId = tenantsService.createTenants(createReqVO);
        return success(TenantsId);
    }

    @PostMapping("update")
    @Operation(summary = "更新租户")
    //@PreAuthorize("@ss.hasPermission('system:tenants:update')")
    public CommonResult<Boolean> updateTenants(@Valid @RequestBody TenantsSaveReqVO updateReqVO) {
        tenantsService.updateTenants(updateReqVO);
        return success(true);
    }

    @PostMapping("delete")
    @Operation(summary = "删除租户")
    //@PreAuthorize("@ss.hasPermission('system:tenants:delete')")
    public CommonResult<Boolean> deleteTenants(@Valid @RequestBody TenantsSaveReqVO updateReqVO) {
        tenantsService.deleteTenants(updateReqVO.getId());
        return success(true);
    }

    @GetMapping("/list")
    @Operation(summary = "获取租户列表")
    //@PreAuthorize("@ss.hasPermission('system:tenants:query')")
    public CommonResult<PageResult<TenantsRespVO>> getTenantsList(TenantsListReqVO reqVO) {

        PageResult<TenantsDO> list = tenantsService.getTenantsPageList(reqVO);
        PageResult<TenantsRespVO> resPage = TenantsConvert.convertTenantsRespVO(list);

        return success(resPage);
    }

    @GetMapping(value = {"/list-all-simple", "/simple-list"})
    @Operation(summary = "获取租户精简信息列表", description = "只包含被开启的租户，主要用于前端的下拉选项")
    public CommonResult<List<TenantsSimpleRespVO>> getSimpleTenantsList() {
        List<TenantsDO> list = tenantsService.getTenantsList( new TenantsListReqVO().setStatus(""));
        return success(BeanUtils.toBean(list, TenantsSimpleRespVO.class));
    }

    @GetMapping("/get")
    @Operation(summary = "获得租户信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('system:tenants:query')")
    public CommonResult<TenantsRespVO> getTenants(@RequestParam("id") String id) {
        TenantsDO tenants = tenantsService.getTenants(id);
        return success(BeanUtils.toBean(tenants, TenantsRespVO.class));
    }

    @PostMapping("bind")
    @Operation(summary = "分配用户")
    //@PreAuthorize("@ss.hasPermission('system:tenants:bind')")
    public CommonResult<String> bindUserToTenants(@Valid @RequestBody TenantsAccountReqVO createReqVO) {
        String TenantsId = tenantsService.bindUserToTenants(createReqVO);
        return success(TenantsId);
    }

    @PostMapping("unassign")
    @Operation(summary = "取消分配用户")
    //@PreAuthorize("@ss.hasPermission('system:tenants:bind')")
    public CommonResult<Boolean> unassignUserToTenants(@Valid @RequestBody TenantsAccountUnassignVO reqVO) {
        tenantsService.unassignUserToTenants(reqVO);
        return success(true);
    }

}
