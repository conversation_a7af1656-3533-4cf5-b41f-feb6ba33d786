package com.szcares.aps.module.agent.controller.admin.entryexit.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 员工入职离职登记表 Excel 导出 VO
 *
 * <AUTHOR>
 */
@ExcelIgnoreUnannotated
@Data
public class EntryExitExportExcelVO {

    @ExcelProperty("主键ID")
    private Long id;

    @ExcelProperty("员工姓名")
    private String employeeName;

    @ExcelProperty("变动类型")
    private String movementType;

    @ExcelProperty("变动日期")
    private String movementDate;

    @ExcelProperty("年份")
    private Integer year;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}