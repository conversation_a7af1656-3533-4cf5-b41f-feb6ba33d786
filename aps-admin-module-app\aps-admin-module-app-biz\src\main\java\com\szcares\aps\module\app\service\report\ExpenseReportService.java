package com.szcares.aps.module.app.service.report;

import com.szcares.aps.module.app.controller.admin.quoto.vo.GetQuotaManagementDataResponse;
import com.szcares.aps.module.app.controller.admin.report.vo.*;
import com.szcares.aps.module.app.controller.admin.quoto.vo.PageResult;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

public interface ExpenseReportService {

    /**
     * 获取账号额度排名
     *
     * @param req
     * @return
     */
    PageResult<GetQuotaManagementDataResponse> getAccountQuotaRanking(GetRankingDataCommonReq req);

    /**
     * 获取应用配额排名
     *
     * @param req
     * @return
     */
    PageResult<GetAppQuotaRankingDataRes> getAppQuotaRankingData(GetRankingDataCommonReq req);

    /**
     * 获取应用密钥配额排名
     *
     * @param req
     * @return
     */
    PageResult<GetAppTokenQuotaRankingDataRes> getAppTokenQuotaRankingData(GetRankingDataCommonReq req);

    /**
     * 获取每天密钥花费数据
     *
     * @return
     */
    List<GetAppTokenDailyQuotaDataRes> getAppTokenDailyQuotaData();

    /**
     * 获取每天ai图片花费数据
     *
     * @param req
     * @return
     */
    PageResult<GetAiImageQuotaRankingRes> getAiImageQuotaRankingData(GetAiImageQuotaRankingDataReq req);

    /**
     * 导出报表
     *
     * @param
     * @return
     */
    List<MessageExportDTO> getMessagesForExport(String tenantName);
    void exportToExcel(List<MessageExportDTO> messages, HttpServletResponse response);
}
