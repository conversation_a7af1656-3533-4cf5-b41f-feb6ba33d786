package com.szcares.aps.module.app.controller.admin.license.vo;

import com.szcares.aps.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/6/5 16:07
 */
@Schema(description = "管理后台 - 授权码分页VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LicenseReqVO{

    @Schema(description = "租户id")
    private String tenantId;

    @Schema(description = "授权码")
    private String jwtToken;
}
