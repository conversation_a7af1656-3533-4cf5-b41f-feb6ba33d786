package com.szcares.aps.module.agent.framework.security.config;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.szcares.aps.module.agent.controller.admin.attendance.vo.AttendanceImportExcelVO;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 考勤Excel导入监听器
 * 用于处理有两行标题的Excel文件，跳过第一行合并标题，从第二行开始读取数据
 *
 * <AUTHOR>
 */
@Slf4j
public class AttendanceExcelListener implements ReadListener<AttendanceImportExcelVO> {

    /**
     * 数据列表
     */
    private final List<AttendanceImportExcelVO> dataList = new ArrayList<>();

    /**
     * 当前行号
     */
    private int currentRow = 0;

    @Override
    public void invoke(AttendanceImportExcelVO data, AnalysisContext context) {
        currentRow++;
        log.debug("正在读取第{}行数据: {}", currentRow, data);
        
        // 跳过空行或无效数据
        if (data == null || (data.getEmployeeName() == null || data.getEmployeeName().trim().isEmpty())) {
            log.debug("第{}行数据为空，跳过", currentRow);
            return;
        }
        
        dataList.add(data);
        log.debug("第{}行数据读取成功: 员工姓名={}, 考勤日期={}", 
                currentRow, data.getEmployeeName(), data.getAttendanceDate());
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("Excel读取完成，共读取{}行有效数据", dataList.size());
    }

    /**
     * 获取读取到的数据列表
     *
     * @return 数据列表
     */
    public List<AttendanceImportExcelVO> getDataList() {
        return dataList;
    }
}
