package com.szcares.aps.module.agent.controller.admin.entryexit.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - 员工入职离职登记表新增/修改 Request VO")
@Data
public class EntryExitSaveReqVO {

    @Schema(description = "主键ID", example = "1024")
    private Long id;

    @Schema(description = "员工姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "员工姓名不能为空")
    private String employeeName;

    @Schema(description = "变动类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "入职")
    @NotEmpty(message = "变动类型不能为空")
    private String movementType;

    @Schema(description = "变动日期", example = "2024-01-01")
    private String movementDate;

}