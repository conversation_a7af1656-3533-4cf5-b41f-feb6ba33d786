package com.szcares.aps.module.agent.service.attendance;

import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.module.agent.controller.admin.attendance.vo.AttendanceExportExcelVO;
import com.szcares.aps.module.agent.controller.admin.attendance.vo.AttendanceImportExcelVO;
import com.szcares.aps.module.agent.controller.admin.attendance.vo.AttendanceImportRespVO;
import com.szcares.aps.module.agent.controller.admin.attendance.vo.AttendancePageReqVO;
import com.szcares.aps.module.agent.controller.admin.attendance.vo.AttendanceRespVO;
import com.szcares.aps.module.agent.controller.admin.attendance.vo.AttendanceSaveReqVO;
import com.szcares.aps.module.agent.dal.dataobject.attendance.AttendanceDO;

import java.util.List;

/**
 * 员工考勤登记表 Service 接口
 *
 * <AUTHOR>
 */
public interface AttendanceService {

    /**
     * 创建员工考勤登记表
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAttendance(AttendanceSaveReqVO createReqVO);

    /**
     * 更新员工考勤登记表
     *
     * @param updateReqVO 更新信息
     */
    void updateAttendance(AttendanceSaveReqVO updateReqVO);

    /**
     * 删除员工考勤登记表
     *
     * @param id 编号
     */
    void deleteAttendance(Long id);

    /**
     * 获得员工考勤登记表
     *
     * @param id 编号
     * @return 员工考勤登记表
     */
    AttendanceDO getAttendance(Long id);

    /**
     * 获得员工考勤登记表分页
     *
     * @param pageReqVO 分页查询
     * @return 员工考勤登记表分页
     */
    PageResult<AttendanceRespVO> getAttendancePage(AttendancePageReqVO pageReqVO);

    /**
     * 获得员工考勤登记表列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 员工考勤登记表列表
     */
    List<AttendanceDO> getAttendanceList(AttendancePageReqVO exportReqVO);

    /**
     * 获得员工考勤登记表 Excel 导出列表
     *
     * @param exportReqVO 查询条件
     * @return 员工考勤登记表 Excel 导出列表
     */
    List<AttendanceExportExcelVO> getAttendanceExportList(AttendancePageReqVO exportReqVO);

    /**
     * 导入员工考勤登记表 Excel
     *
     * @param importReqVO 导入信息
     * @return 导入结果
     */
    AttendanceImportRespVO importAttendanceList(List<AttendanceImportExcelVO> importReqVO);

}