package com.szcares.aps.module.app.dal.dataobject.quoto;


import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 额度表
 *
 * <AUTHOR>
 */
@TableName("account_money_extend")
@Data
public class QuotoDO {

    /**
     * 用户id
     */
    @TableId(type = IdType.ASSIGN_UUID)
   // @TableField(value = "id", jdbcType = JdbcType.VARCHAR)
    private String id;
    //private UUID id;

    /**
     * 账户id
     */
    //@TableField(value = "account_id", jdbcType = JdbcType.VARCHAR)
    private String accountId;
    //private UUID accountId;

    /**
     * 额度
     */
    private BigDecimal totalQuota;

    /**
     * 已使用额度
     */
    private BigDecimal usedQuota;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

}
