package com.szcares.aps.module.app.dal.dataobject.pricing;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fhs.core.trans.vo.TransPojo;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
@TableName("pricing_rule_log")
@Data
public class PricingRuleLogDO implements Serializable, TransPojo {
    private Long id;
    private String operator;
    private LocalDateTime operateTime;
    private String operateType;
    private String operateContent;
}