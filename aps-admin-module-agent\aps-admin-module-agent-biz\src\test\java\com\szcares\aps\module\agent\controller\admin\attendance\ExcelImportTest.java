package com.szcares.aps.module.agent.controller.admin.attendance;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.szcares.aps.module.agent.controller.admin.attendance.vo.AttendanceImportExcelVO;
import com.szcares.aps.module.agent.framework.security.config.AttendanceExcelListener;
import org.junit.jupiter.api.Test;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.List;

/**
 * Excel导入测试类
 * 用于测试考勤Excel导入功能
 */
public class ExcelImportTest {

    @Test
    public void testExcelImport() throws IOException {
        // 注意：这里需要替换为实际的Excel文件路径
        String filePath = "D:\\test-attendance.xlsx";
        
        try {
            // 使用专门的监听器处理有两行标题的Excel文件
            AttendanceExcelListener listener = new AttendanceExcelListener();
            EasyExcel.read(new FileInputStream(filePath), AttendanceImportExcelVO.class, listener)
                    .excelType(ExcelTypeEnum.XLSX) // 指定文件的导入类型
                    .sheet(0) // 读取sheet页码，页码从0开始
                    .headRowNumber(2) // 设置标题行为第2行（0-based，所以实际是第3行），跳过前两行合并标题
                    .autoTrim(true) // 读取数据时自动去除空格
                    .doRead();
            
            // 获取读取到的数据
            List<AttendanceImportExcelVO> datas = listener.getDataList();
            
            System.out.println("读取到的数据行数: " + datas.size());
            
            // 打印前几行数据进行验证
            for (int i = 0; i < Math.min(5, datas.size()); i++) {
                AttendanceImportExcelVO data = datas.get(i);
                System.out.println("第" + (i + 1) + "行数据:");
                System.out.println("  员工姓名: " + data.getEmployeeName());
                System.out.println("  考勤组: " + data.getAttendanceGroup());
                System.out.println("  部门: " + data.getDepartment());
                System.out.println("  UserId: " + data.getUserId());
                System.out.println("  日期: " + data.getAttendanceDate());
                System.out.println("  workDate: " + data.getWorkDate());
                System.out.println("  班次: " + data.getShift());
                System.out.println("  上班1打卡时间: " + data.getWork1CheckinTime());
                System.out.println("  上班1打卡结果: " + data.getWork1CheckinResult());
                System.out.println("  下班1打卡时间: " + data.getWork1CheckoutTime());
                System.out.println("  下班1打卡结果: " + data.getWork1CheckoutResult());
                System.out.println("  外出时长: " + data.getOutingDuration());
                System.out.println("  病假天数: " + data.getSickLeaveDays());
                System.out.println("  年假天数: " + data.getAnnualLeaveDays());
                System.out.println("  事假天数: " + data.getPersonalLeaveDays());
                System.out.println("  加班总时长: " + data.getTotalOvertimeDuration());
                System.out.println("---");
            }
            
        } catch (Exception e) {
            System.err.println("Excel导入失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    @Test
    public void testExcelImportWithDifferentHeadRowNumber() throws IOException {
        // 测试不同的headRowNumber设置
        String filePath = "D:\\test-attendance.xlsx";
        
        for (int headRowNumber = 0; headRowNumber <= 3; headRowNumber++) {
            System.out.println("\n=== 测试 headRowNumber = " + headRowNumber + " ===");
            
            try {
                AttendanceExcelListener listener = new AttendanceExcelListener();
                EasyExcel.read(new FileInputStream(filePath), AttendanceImportExcelVO.class, listener)
                        .excelType(ExcelTypeEnum.XLSX)
                        .sheet(0)
                        .headRowNumber(headRowNumber)
                        .autoTrim(true)
                        .doRead();
                
                List<AttendanceImportExcelVO> datas = listener.getDataList();
                System.out.println("读取到的数据行数: " + datas.size());
                
                if (!datas.isEmpty()) {
                    AttendanceImportExcelVO firstRow = datas.get(0);
                    System.out.println("第一行数据:");
                    System.out.println("  员工姓名: " + firstRow.getEmployeeName());
                    System.out.println("  考勤组: " + firstRow.getAttendanceGroup());
                    System.out.println("  部门: " + firstRow.getDepartment());
                    System.out.println("  UserId: " + firstRow.getUserId());
                }
                
            } catch (Exception e) {
                System.err.println("headRowNumber=" + headRowNumber + " 时导入失败: " + e.getMessage());
            }
        }
    }
}
