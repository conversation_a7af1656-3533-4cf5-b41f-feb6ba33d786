package com.szcares.aps.module.app.service.license;

import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.module.app.controller.admin.license.vo.LicenseInfoVO;
import com.szcares.aps.module.app.controller.admin.license.vo.LicenseRespVO;
import com.szcares.aps.module.app.dal.dataobject.license.TenantConfigDO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/4 14:05
 */

public interface LicenseServise {

    public int generateLicense(TenantConfigDO tenantConfigDO) throws Exception;

    public String getLicense(String tenantsId,String jwtToken,String clientIp);

    public int updateLicense(TenantConfigDO tenantConfigDO) throws Exception;

    LicenseInfoVO decryptAuthorizationCode(String encryptedJson) throws Exception;

    PageResult<TenantConfigDO> getUserPage(LicenseRespVO reqVO);

    List<TenantConfigDO> getUserList(String tenantsId);
}
