package com.szcares.aps.module.agent.controller.admin.attendance;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.szcares.aps.module.agent.controller.admin.attendance.vo.AttendanceImportExcelVO;
import org.junit.jupiter.api.Test;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.List;

/**
 * 简单的Excel导入测试类
 * 用于测试基本的EasyExcel功能
 */
public class SimpleExcelImportTest {

    @Test
    public void testBasicExcelImport() throws IOException {
        // 注意：这里需要替换为实际的Excel文件路径
        String filePath = "D:\\test-attendance.xlsx";
        
        try {
            // 基本的EasyExcel读取，不使用自定义监听器
            List<AttendanceImportExcelVO> datas = EasyExcel.read(new FileInputStream(filePath), AttendanceImportExcelVO.class, null)
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet(0)
                    .headRowNumber(2) // 跳过前两行
                    .autoTrim(true)
                    .doReadAllSync();
            
            System.out.println("读取到的数据行数: " + datas.size());
            
            // 打印前几行数据进行验证
            for (int i = 0; i < Math.min(3, datas.size()); i++) {
                AttendanceImportExcelVO data = datas.get(i);
                System.out.println("第" + (i + 1) + "行数据:");
                System.out.println("  员工姓名: " + data.getEmployeeName());
                System.out.println("  考勤组: " + data.getAttendanceGroup());
                System.out.println("  部门: " + data.getDepartment());
                System.out.println("  UserId: " + data.getUserId());
                System.out.println("  日期: " + data.getAttendanceDate());
                System.out.println("  workDate: " + data.getWorkDate());
                System.out.println("  班次: " + data.getShift());
                System.out.println("---");
            }
            
        } catch (Exception e) {
            System.err.println("Excel导入失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    @Test
    public void testWithoutHeadRowNumber() throws IOException {
        // 测试不设置headRowNumber的情况
        String filePath = "D:\\test-attendance.xlsx";
        
        try {
            List<AttendanceImportExcelVO> datas = EasyExcel.read(new FileInputStream(filePath), AttendanceImportExcelVO.class, null)
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet(0)
                    .autoTrim(true)
                    .doReadAllSync();
            
            System.out.println("不设置headRowNumber - 读取到的数据行数: " + datas.size());
            
            if (!datas.isEmpty()) {
                AttendanceImportExcelVO firstRow = datas.get(0);
                System.out.println("第一行数据:");
                System.out.println("  员工姓名: " + firstRow.getEmployeeName());
                System.out.println("  考勤组: " + firstRow.getAttendanceGroup());
                System.out.println("  部门: " + firstRow.getDepartment());
                System.out.println("  UserId: " + firstRow.getUserId());
            }
            
        } catch (Exception e) {
            System.err.println("Excel导入失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
