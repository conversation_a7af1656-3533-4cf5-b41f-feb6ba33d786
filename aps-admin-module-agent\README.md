# APS Admin Module Agent

## 概述

APS Admin Module Agent 是一个员工管理模块，提供员工入职离职信息和考勤信息的管理功能。

## 功能特性

- 员工入职离职信息管理
- 员工考勤信息管理
- Excel 导入导出功能
- 数据去重和容错处理

## 数据库表结构

### employee_movements（员工入职离职登记表）

```sql
CREATE TABLE employee_movements (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `employee_name` VARCHAR(255) NOT NULL COMMENT '员工姓名',
  `movement_type` ENUM('入职', '离职') NOT NULL COMMENT '变动类型',
  `movement_date` VARCHAR(20) COMMENT '变动日期',
  `year` INT GENERATED ALWAYS AS (YEAR(movement_date)) STORED COMMENT '年份',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '生成时间',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `updater` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改者',
  INDEX idx_name (employee_name),
  INDEX idx_type_date (movement_type, movement_date),
  INDEX idx_year (year),
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='员工入职离职登记表';
```

### attendance_table_extend（员工考勤登记表）

```sql
CREATE TABLE attendance_table_extend (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `employee_name` VARCHAR(255) NOT NULL COMMENT '员工姓名',
  `attendance_group` VARCHAR(255) COMMENT '考勤组',
  `department` VARCHAR(255) COMMENT '所属部门',
  `user_id` VARCHAR(255) COMMENT '用户ID',
  `attendance_date` VARCHAR(20) COMMENT '考勤日期',
  -- ... 其他字段
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='员工考勤登记表';
```

## API 接口

### 员工入职离职管理

- `POST /agent/entry-exit/create` - 创建员工入职离职记录
- `PUT /agent/entry-exit/update` - 更新员工入职离职记录
- `DELETE /agent/entry-exit/delete` - 删除员工入职离职记录
- `GET /agent/entry-exit/get` - 获取员工入职离职记录
- `GET /agent/entry-exit/page` - 分页查询员工入职离职记录
- `GET /agent/entry-exit/export-excel` - 导出员工入职离职记录
- `POST /agent/entry-exit/import` - 导入员工入职离职记录
- `GET /agent/entry-exit/get-import-template` - 获取导入模板

### 员工考勤管理

- `POST /agent/attendance/create` - 创建员工考勤记录
- `PUT /agent/attendance/update` - 更新员工考勤记录
- `DELETE /agent/attendance/delete` - 删除员工考勤记录
- `GET /agent/attendance/get` - 获取员工考勤记录
- `GET /agent/attendance/page` - 分页查询员工考勤记录
- `GET /agent/attendance/export-excel` - 导出员工考勤记录
- `POST /agent/attendance/import` - 导入员工考勤记录
- `GET /agent/attendance/get-import-template` - 获取导入模板

## 数据导入规则

### 员工入职离职信息导入
- 去重规则：通过 `employee_name`（员工姓名）判断
- 必填字段：`employee_name`（员工姓名）、`movement_type`（变动类型）
- 容错处理：跳过不符合要求的数据行，继续处理其他数据

### 员工考勤信息导入
- 去重规则：通过 `employee_name`（员工姓名）+ `attendance_date`（考勤日期）判断
- 必填字段：`employee_name`（员工姓名）、`attendance_date`（考勤日期）
- 容错处理：跳过不符合要求的数据行，继续处理其他数据

## 数据库迁移

如果您从旧版本升级，需要执行数据库迁移脚本：

1. 备份现有数据库
2. 执行 `sql/mysql/aps-admin-module-agent-migration.sql` 中的迁移脚本
3. 验证数据迁移结果
4. 确认无误后删除旧表

## 变更记录

### v1.1.0
- 将表名从 `entry_exit_table_extend` 更改为 `employee_movements`
- 将字段名从 `exit_date` 更改为 `movement_date`
- 优化了表结构和索引

### v1.0.0
- 初始版本发布
- 支持员工入职离职信息管理
- 支持员工考勤信息管理
- 支持 Excel 导入导出功能