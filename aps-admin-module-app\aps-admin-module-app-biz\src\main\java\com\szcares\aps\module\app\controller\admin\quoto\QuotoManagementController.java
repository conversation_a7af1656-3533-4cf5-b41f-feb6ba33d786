package com.szcares.aps.module.app.controller.admin.quoto;


import com.szcares.aps.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.szcares.aps.framework.common.pojo.CommonResult;
import com.szcares.aps.framework.common.util.object.BeanUtils;
import com.szcares.aps.module.app.controller.admin.quoto.vo.GetAccountQuotaRankingDataReq;
import com.szcares.aps.module.app.controller.admin.quoto.vo.GetQuotaManagementDataResponse;
import com.szcares.aps.module.app.controller.admin.quoto.vo.PageResult;
import com.szcares.aps.module.app.controller.admin.quoto.vo.SetUserQuotaReq;
import com.szcares.aps.module.app.service.quoto.QuotoManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import static com.szcares.aps.framework.common.pojo.CommonResult.success;

import java.util.UUID;

@Tag(name = "管理后台 - 额度管理")
@RestController
@RequestMapping("/app/quoto")
@Validated
public class QuotoManagementController {

    @Resource
    private QuotoManagementService quotoManagementService;

    @PutMapping("/setUserQuota")
    @Operation(summary = "设置用户额度")
    @PreAuthorize("@ss.hasPermission('system:quota:update')")
    public CommonResult<String> setUserQuota(@Valid @RequestBody SetUserQuotaReq req){
            //UUID  uuid = UUID.fromString(req.getId());
           quotoManagementService.setUserQuota(req);
           return CommonResult.success("设置成功");
    }

    @GetMapping("/quotaManagementList")
    @Operation(summary = "额度管理列表")
    @PreAuthorize("@ss.hasPermission('system:quota:query')")
    public CommonResult<PageResult<GetQuotaManagementDataResponse>> getQuotaManagementList(
            @ModelAttribute GetAccountQuotaRankingDataReq pageInfo
    ){
        PageResult<GetQuotaManagementDataResponse> result = quotoManagementService.getQuotaManagementList(pageInfo);
        return success(result);
    }

}
