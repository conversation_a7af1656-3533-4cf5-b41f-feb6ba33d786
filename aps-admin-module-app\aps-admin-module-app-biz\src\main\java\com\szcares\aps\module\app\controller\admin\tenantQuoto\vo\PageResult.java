package com.szcares.aps.module.app.controller.admin.tenantQuoto.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class PageResult<T> {

    @Schema(description = "分页数据")
    private List<T> list;

    @Schema(description = "总条数")
    private Long total;

    @Schema(description = "当前页")
    private Integer page;

    @Schema(description = "每页条数")
    private Integer pageSize;

    public PageResult(List<T> list, Long total, Integer page, Integer pageSize) {
        this.list = list;
        this.total = total;
        this.page = page;
        this.pageSize = pageSize;
    }
}
