package com.szcares.aps.module.app.service.account;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.annotations.VisibleForTesting;
import com.szcares.aps.framework.common.exception.util.ServiceExceptionUtil;
import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.framework.common.util.object.BeanUtils;
import com.szcares.aps.module.app.controller.admin.account.vo.AccountListByTenantReqVO;
import com.szcares.aps.module.app.controller.admin.account.vo.AccountListReqVO;
import com.szcares.aps.module.app.controller.admin.account.vo.AccountPageReqVO;
import com.szcares.aps.module.app.controller.admin.account.vo.AccountSaveReqVO;
import com.szcares.aps.module.app.dal.dataobject.account.AccountDO;
import com.szcares.aps.module.app.dal.dataobject.quoto.QuotoDO;
import com.szcares.aps.module.app.dal.postgresql.account.AccountMapper;
import com.szcares.aps.module.app.dal.postgresql.quoto.QuotaManagementMapper;
import com.szcares.aps.module.app.util.PasswordUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;

import static com.szcares.aps.module.system.enums.ErrorCodeConstants.USER_EMAIL_EXISTS;
import static com.szcares.aps.module.system.enums.ErrorCodeConstants.USER_NOT_EXISTS;

@Service
@Validated
@Slf4j
public class AccountServiceImpl implements AccountService {

    @Resource
    private AccountMapper accountMapper;

    @Resource
    private QuotaManagementMapper quotaManagementMapper;

    @Override
    @DS("apsds")
    @Transactional
    public String createAccount(AccountSaveReqVO createReqVO) {
        validateAccountNameUnique(null, createReqVO.getEmail());

        AccountDO account = BeanUtils.toBean(createReqVO, AccountDO.class);
        String uuid = java.util.UUID.randomUUID().toString();
        account.setId(uuid);
        account.setCreatedAt(LocalDateTime.now());
        account.setUpdatedAt(LocalDateTime.now());
        account.setStatus("active");
        account.setEmail(createReqVO.getEmail());
        account.setName(createReqVO.getName());
        account.setTimezone(createReqVO.getTimezone().trim());

        try {
            if (createReqVO.getPassword() != null && !createReqVO.getPassword().isEmpty()) {
                // 生成16字节的随机salt
                byte[] salt = PasswordUtil.generateSalt(16);
                // 使用自定义的 hashPassword 方法对密码进行加密
                String base64Salt = java.util.Base64.getEncoder().encodeToString(salt);
                String base64PasswordHashed = PasswordUtil.hashPasswordHex(createReqVO.getPassword(), salt);

                account.setPassword(base64PasswordHashed);
                account.setPasswordSalt(base64Salt);
            }
        }catch (Exception e){
            log.error("密码加密失败", e);
        }

        account.setInterfaceLanguage(createReqVO.getInterfaceLanguage());
        account.setInterfaceTheme(createReqVO.getInterfaceTheme());

        // 根据语言设置时区，假设 languageTimezoneMapping 是一个 Map<String, String>
        //String timezone = languageTimezoneMapping.getOrDefault(interfaceLanguage, "UTC");
        //account.setTimezone(timezone);

        accountMapper.insertByAccountDO(account);

        //新增用户额度
        QuotoDO quotoDO = new QuotoDO();
        quotoDO.setId(java.util.UUID.randomUUID().toString());
        quotoDO.setAccountId(uuid);
        quotoDO.setTotalQuota(new BigDecimal("10"));
        quotoDO.setUsedQuota(new BigDecimal("0"));
        quotaManagementMapper.insert(quotoDO);

        return account.getId();
    }

    @Override
    @DS("apsds")
    public void updateAccount(AccountSaveReqVO updateReqVO) {
        validateAccountExists(updateReqVO.getId());
        validateAccountNameUnique(updateReqVO.getId(), updateReqVO.getEmail());

        AccountDO updateObj = BeanUtils.toBean(updateReqVO, AccountDO.class);
        updateObj.setUpdatedAt(LocalDateTime.now());
        accountMapper.updateByAccountDO(updateObj);
    }

    @Override
    @DS("apsds")
    public void updateAccountPwd(AccountSaveReqVO updateReqVO) {
        validateAccountExists(updateReqVO.getId());
        AccountDO updateObj = BeanUtils.toBean(updateReqVO, AccountDO.class);
        try {
            if (updateReqVO.getPassword() != null && !updateReqVO.getPassword().isEmpty()) {
                // 生成16字节的随机salt
                byte[] salt = PasswordUtil.generateSalt(16);
                // 使用自定义的 hashPassword 方法对密码进行加密
                String base64Salt = java.util.Base64.getEncoder().encodeToString(salt);
                String base64PasswordHashed = PasswordUtil.hashPasswordHex(updateReqVO.getPassword(), salt);
                updateObj.setPassword(base64PasswordHashed);
                updateObj.setPasswordSalt(base64Salt);
            }
        }catch (Exception e){
            log.error("密码加密失败", e);
        }
        updateObj.setUpdatedAt(LocalDateTime.now());
        accountMapper.updateByPWD(updateObj);
    }

    @Override
    @DS("apsds")
    public void deleteAccount(String id) {
        validateAccountExists(id);
        accountMapper.deleteByAccountId(id);
    }

    @Override
    @DS("apsds")
    public AccountDO getAccount(String id) {
        return accountMapper.selectById(id);
    }

    @Override
    @DS("apsds")
    public PageResult<AccountDO> getAccountPageList(AccountPageReqVO reqVO) {
        //状态：已激活
        PageResult<AccountDO> list = accountMapper.selectPage(reqVO);
        return list;
    }

    @Override
    @DS("apsds")
    public List<AccountDO> getAccountList(AccountListReqVO reqVO) {
        List<AccountDO> list = accountMapper.selectList(reqVO);
        list.sort(Comparator.comparing(AccountDO::getName));
        return list;
    }

    @Override
    @DS("apsds")
    public List<AccountDO> getSimpleAccountListByTenant(AccountListByTenantReqVO reqVO) {
        List<AccountDO> list = accountMapper.getSimpleAccountListByTenant(reqVO);
        return list;
    }

    @VisibleForTesting
    @DS("apsds")
    void validateAccountExists(String id) {
        if (id == null) return;
        AccountDO account = accountMapper.selectById(id);
        if (account == null) {
            throw ServiceExceptionUtil.exception(USER_NOT_EXISTS);
        }
    }

    @VisibleForTesting
    @DS("apsds")
    void validateAccountNameUnique(String id, String email) {
        AccountDO account = accountMapper.selectByEmail(email);
        if (account == null) return;
        if (id == null || !ObjectUtil.equal(account.getId(), id)) {
            throw ServiceExceptionUtil.exception(USER_EMAIL_EXISTS);
        }
    }
}
