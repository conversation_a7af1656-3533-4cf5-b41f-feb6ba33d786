package com.szcares.aps.module.app.dal.postgresql.tenants;

import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.framework.mybatis.core.mapper.BaseMapperX;
import com.szcares.aps.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.szcares.aps.module.app.controller.admin.tenants.vo.TenantsListReqVO;
import com.szcares.aps.module.app.dal.dataobject.tenants.TenantsAccountJoinsDO;
import com.szcares.aps.module.app.dal.dataobject.tenants.TenantsDO;
import org.apache.ibatis.annotations.*;

@Mapper
public interface TenantsAccountJoinsMapper extends BaseMapperX<TenantsAccountJoinsDO> {

    @Insert("INSERT INTO public.tenant_account_joins" +
            "(id, tenant_id, account_id, role )" +
            "VALUES( #{entity.id}::uuid, #{entity.tenantId}::uuid, #{entity.accountId}::uuid, #{entity.role} )")
    int insertJoinsDO(@Param("entity") TenantsAccountJoinsDO entity);

    //    @Update("UPDATE public.tenants SET " +
//            "name = #{entity.name}, " +
//            "updated_at = #{entity.updatedAt}" +
//            "WHERE id = #{entity.id}::uuid")
//    int updateByTenantsDO(@Param("entity") TenantsDO entity);
//
//    @Update("UPDATE public.tenants SET " +
//            "status = #{entity.status}, " +
//            "updated_at = #{entity.updatedAt}" +
//            "WHERE id = #{entity.id}::uuid")
//    int updateStatusById(@Param("entity") TenantsDO entity);
//
    @Delete("DELETE FROM public.tenant_account_joins WHERE id = #{id}::uuid")
    int deleteByUUId(String id);

    @Select("SELECT * FROM public.tenant_account_joins WHERE tenant_id = #{tenantId}::uuid AND role = 'owner' ")
    TenantsAccountJoinsDO selectByRoleAndTenantId(@Param("tenantId") String tenantId);
}