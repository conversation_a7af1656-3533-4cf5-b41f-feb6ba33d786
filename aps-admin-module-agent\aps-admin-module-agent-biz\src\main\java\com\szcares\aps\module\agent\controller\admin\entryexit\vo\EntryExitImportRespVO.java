package com.szcares.aps.module.agent.controller.admin.entryexit.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 员工入职离职登记表导入 Response VO")
@Data
public class EntryExitImportRespVO {

    @Schema(description = "创建成功的员工姓名列表")
    private List<String> createNames;

    @Schema(description = "更新成功的员工姓名列表")
    private List<String> updateNames;

    @Schema(description = "导入失败的员工姓名和原因列表")
    private List<String> failureNames;

}