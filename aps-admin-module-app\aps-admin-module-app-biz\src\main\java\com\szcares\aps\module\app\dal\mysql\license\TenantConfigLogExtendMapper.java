package com.szcares.aps.module.app.dal.mysql.license;

import com.szcares.aps.framework.mybatis.core.mapper.BaseMapperX;
import com.szcares.aps.module.app.dal.dataobject.license.TenantConfigDO;
import com.szcares.aps.module.app.dal.dataobject.license.TenantConfigLogExtendDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 租户授权码日志表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantConfigLogExtendMapper extends BaseMapperX<TenantConfigLogExtendDO> {

}
