package com.szcares.aps.module.app.dal.dataobject.tenants;

import com.baomidou.mybatisplus.annotation.*;
import com.fhs.core.trans.vo.TransPojo;
import com.szcares.aps.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 部门表
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@TableName("tenants")
@Data
public class TenantsDO implements Serializable, TransPojo {

    public static final Long PARENT_ID_ROOT = 0L;

    private String id;

    private String name;

    private String encryptPublicKey;

    private String plan;

    private String status;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private String customConfig;

}
