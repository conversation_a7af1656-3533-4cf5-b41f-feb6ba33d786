package com.szcares.aps.module.agent.service.entryexit;

import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.framework.common.util.object.BeanUtils;
import com.szcares.aps.module.agent.controller.admin.entryexit.vo.EntryExitExportExcelVO;
import com.szcares.aps.module.agent.controller.admin.entryexit.vo.EntryExitImportExcelVO;
import com.szcares.aps.module.agent.controller.admin.entryexit.vo.EntryExitImportRespVO;
import com.szcares.aps.module.agent.controller.admin.entryexit.vo.EntryExitPageReqVO;
import com.szcares.aps.module.agent.controller.admin.entryexit.vo.EntryExitRespVO;
import com.szcares.aps.module.agent.controller.admin.entryexit.vo.EntryExitSaveReqVO;
import com.szcares.aps.module.agent.convert.entryexit.EntryExitConvert;
import com.szcares.aps.module.agent.dal.dataobject.entryexit.EntryExitDO;
import com.szcares.aps.module.agent.dal.mysql.entryexit.EntryExitMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;

import static com.szcares.aps.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.szcares.aps.module.agent.enums.ErrorCodeConstants.ENTRY_EXIT_NOT_EXISTS;

/**
 * 员工入职离职登记表 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class EntryExitServiceImpl implements EntryExitService {

    @Resource
    private EntryExitMapper entryExitMapper;

    @Override
    public Long createEntryExit(EntryExitSaveReqVO createReqVO) {
        // 插入
        EntryExitDO entryExit = BeanUtils.toBean(createReqVO, EntryExitDO.class);
        entryExitMapper.insert(entryExit);
        // 返回
        return entryExit.getId();
    }

    @Override
    public void updateEntryExit(EntryExitSaveReqVO updateReqVO) {
        // 校验存在
        validateEntryExitExists(updateReqVO.getId());
        // 更新
        EntryExitDO updateObj = BeanUtils.toBean(updateReqVO, EntryExitDO.class);
        entryExitMapper.updateById(updateObj);
    }

    @Override
    public void deleteEntryExit(Long id) {
        // 校验存在
        validateEntryExitExists(id);
        // 删除
        entryExitMapper.deleteById(id);
    }

    private void validateEntryExitExists(Long id) {
        if (entryExitMapper.selectById(id) == null) {
            throw exception(ENTRY_EXIT_NOT_EXISTS);
        }
    }

    @Override
    public EntryExitDO getEntryExit(Long id) {
        return entryExitMapper.selectById(id);
    }

    @Override
    public PageResult<EntryExitRespVO> getEntryExitPage(EntryExitPageReqVO pageReqVO) {
        PageResult<EntryExitDO> pageResult = entryExitMapper.selectPage(pageReqVO);
        return EntryExitConvert.INSTANCE.convertPage(pageResult);
    }

    @Override
    public List<EntryExitDO> getEntryExitList(EntryExitPageReqVO exportReqVO) {
        return entryExitMapper.selectList(exportReqVO);
    }

    @Override
    public List<EntryExitExportExcelVO> getEntryExitExportList(EntryExitPageReqVO exportReqVO) {
        List<EntryExitDO> list = entryExitMapper.selectList(exportReqVO);
        return EntryExitConvert.INSTANCE.convertList02(list);
    }

    @Override
    public EntryExitImportRespVO importEntryExitList(List<EntryExitImportExcelVO> importReqVO) {
        EntryExitImportRespVO respVO = new EntryExitImportRespVO();
        respVO.setCreateNames(new ArrayList<>());
        respVO.setUpdateNames(new ArrayList<>());
        respVO.setFailureNames(new ArrayList<>());

        for (EntryExitImportExcelVO excelVO : importReqVO) {
            try {
                // 校验必填字段
                if (excelVO.getEmployeeName() == null || excelVO.getEmployeeName().trim().isEmpty()) {
                    respVO.getFailureNames().add("员工姓名不能为空");
                    continue;
                }
                if (excelVO.getMovementType() == null || excelVO.getMovementType().trim().isEmpty()) {
                    respVO.getFailureNames().add(excelVO.getEmployeeName() + " - 变动类型不能为空");
                    continue;
                }

                // 检查是否已存在（通过员工姓名判断）
                EntryExitDO existingEntryExit = entryExitMapper.selectByEmployeeName(excelVO.getEmployeeName());
                
                if (existingEntryExit != null) {
                    // 更新现有记录
                    existingEntryExit.setMovementType(excelVO.getMovementType());
                    existingEntryExit.setMovementDate(excelVO.getMovementDate());
                    entryExitMapper.updateById(existingEntryExit);
                    respVO.getUpdateNames().add(excelVO.getEmployeeName());
                } else {
                    // 创建新记录
                    EntryExitDO newEntryExit = new EntryExitDO();
                    newEntryExit.setEmployeeName(excelVO.getEmployeeName());
                    newEntryExit.setMovementType(excelVO.getMovementType());
                    newEntryExit.setMovementDate(excelVO.getMovementDate());
                    entryExitMapper.insert(newEntryExit);
                    respVO.getCreateNames().add(excelVO.getEmployeeName());
                }
            } catch (Exception e) {
                log.error("导入员工入职离职信息失败: {}", excelVO.getEmployeeName(), e);
                String employeeName = excelVO.getEmployeeName() != null ? excelVO.getEmployeeName() : "未知";
                respVO.getFailureNames().add(employeeName + " - " + e.getMessage());
            }
        }

        return respVO;
    }

}