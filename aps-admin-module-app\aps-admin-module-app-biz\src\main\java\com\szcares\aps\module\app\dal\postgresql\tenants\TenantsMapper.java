package com.szcares.aps.module.app.dal.postgresql.tenants;

import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.framework.mybatis.core.mapper.BaseMapperX;
import com.szcares.aps.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.szcares.aps.module.app.controller.admin.tenants.vo.TenantsListReqVO;
import com.szcares.aps.module.app.dal.dataobject.tenants.TenantsDO;
import org.apache.ibatis.annotations.*;

import java.util.Collection;
import java.util.List;

@Mapper
public interface TenantsMapper extends BaseMapperX<TenantsDO> {

    default PageResult<TenantsDO> selectPage(TenantsListReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TenantsDO>()
                .likeIfPresent(TenantsDO::getName, reqVO.getName())
                .eqIfPresent(TenantsDO::getStatus, reqVO.getStatus())
                .orderByDesc(TenantsDO::getName));
    }

    default List<TenantsDO> selectList(TenantsListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TenantsDO>()
                .likeIfPresent(TenantsDO::getName, reqVO.getName())
                .eqIfPresent(TenantsDO::getStatus, reqVO.getStatus())
                .orderByDesc(TenantsDO::getName));
    }

    default TenantsDO selectByName(String name) {
        return selectOne(TenantsDO::getName, name);
    }

    default TenantsDO selectByNameAndStatus(String name, String status) {
        return selectOne(new LambdaQueryWrapperX<TenantsDO>()
                .eq(TenantsDO::getName, name)
                .eq(TenantsDO::getStatus, status));
    }

    @Select("select * FROM tenants WHERE id = #{id}::uuid" )
    TenantsDO selectById(String id);

    @Insert("insert INTO public.tenants " +
            "( id, name, encrypt_public_key, plan, status,  custom_config)" +
            "VALUES( #{entity.id}::uuid, #{entity.name}, #{entity.encryptPublicKey}, #{entity.plan}, #{entity.status}, #{entity.customConfig})" )
    int insertByTenantsDO(@Param("entity") TenantsDO entity);

    @Update("UPDATE public.tenants SET " +
            "name = #{entity.name}, " +
            "updated_at = #{entity.updatedAt}" +
            "WHERE id = #{entity.id}::uuid")
    int updateByTenantsDO(@Param("entity") TenantsDO entity);

    @Update("UPDATE public.tenants SET " +
            "status = #{entity.status}, " +
            "updated_at = #{entity.updatedAt}" +
            "WHERE id = #{entity.id}::uuid")
    int updateStatusById(@Param("entity") TenantsDO entity);

    @Delete("DELETE FROM tenants WHERE id = #{id}::uuid" )
    int deleteByUUId(String id);

}
