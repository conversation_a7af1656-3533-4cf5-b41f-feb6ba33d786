package com.szcares.aps.module.app.service.quoto;

import com.szcares.aps.framework.common.exception.ServiceException;
import com.szcares.aps.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.szcares.aps.framework.common.exception.util.ServiceExceptionUtil;
import com.szcares.aps.framework.common.pojo.CommonResult;
import com.szcares.aps.module.app.controller.admin.quoto.vo.GetAccountQuotaRankingDataReq;
import com.szcares.aps.module.app.controller.admin.quoto.vo.GetQuotaManagementDataResponse;
import com.szcares.aps.module.app.controller.admin.quoto.vo.PageResult;
import com.szcares.aps.module.app.controller.admin.quoto.vo.SetUserQuotaReq;
import org.springframework.stereotype.Service;

import java.security.SecureRandomParameters;
import java.util.UUID;

/**
 * 额度管理Service 接口
 *
 * <AUTHOR>
 */

public interface QuotoManagementService {

    /**
     * 设置额度
     *
     * @param req
     * @return
     */
    void setUserQuota(SetUserQuotaReq req);


    /**
     * 获取额度列表
     *
     * @param req
     * @return
     */
    PageResult<GetQuotaManagementDataResponse> getQuotaManagementList(GetAccountQuotaRankingDataReq req);
}
