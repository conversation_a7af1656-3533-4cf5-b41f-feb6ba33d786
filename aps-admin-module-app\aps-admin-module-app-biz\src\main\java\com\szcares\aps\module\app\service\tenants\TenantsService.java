package com.szcares.aps.module.app.service.tenants;

import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.framework.common.util.collection.CollectionUtils;
import com.szcares.aps.module.app.controller.admin.tenants.vo.TenantsAccountReqVO;
import com.szcares.aps.module.app.controller.admin.tenants.vo.TenantsAccountUnassignVO;
import com.szcares.aps.module.app.controller.admin.tenants.vo.TenantsListReqVO;
import com.szcares.aps.module.app.controller.admin.tenants.vo.TenantsSaveReqVO;
import com.szcares.aps.module.app.dal.dataobject.tenants.TenantsDO;

import java.util.*;

/**
 * 租户 Service 接口
 *
 * <AUTHOR>
 */
public interface TenantsService {

    /**
     * 创建租户
     *
     * @param createReqVO 租户信息
     * @return 租户编号
     */
    String createTenants(TenantsSaveReqVO createReqVO);

    /**
     * 更新租户
     *
     * @param updateReqVO 租户信息
     */
    void updateTenants(TenantsSaveReqVO updateReqVO);

    /**
     * 删除租户
     *
     * @param id 租户编号
     */
    void deleteTenants(String id);

    /**
     * 获得租户信息
     *
     * @param id 租户编号
     * @return 租户信息
     */
    TenantsDO getTenants(String id);

    /**
     * 筛选租户列表
     *
     * @param reqVO 筛选条件请求 VO
     * @return 租户列表
     */
    PageResult<TenantsDO> getTenantsPageList(TenantsListReqVO reqVO);

    List<TenantsDO> getTenantsList(TenantsListReqVO reqVO);

    /**
     * 租户绑定用户信息
     *
     * @param reqVO 筛选条件请求 VO
     * @return 租户列表
     */
    String bindUserToTenants(TenantsAccountReqVO reqVO);

    void unassignUserToTenants(TenantsAccountUnassignVO reqVO);

}
