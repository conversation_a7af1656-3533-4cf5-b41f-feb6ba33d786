package com.szcares.aps.module.infra.enums.codegen;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 代码生成器的字段 HTML 展示枚举
 */
@AllArgsConstructor
@Getter
public enum CodegenColumnHtmlTypeEnum {

    INPUT("input"),
    TEXTAREA("textarea"),
    SELECT("select"),
    RADIO("radio"),
    CHECKBOX("checkbox"),
    DATETIME("datetime"),
    IMAGE_UPLOAD("imageUpload"),
    FILE_UPLOAD("fileUpload"),
    EDITOR("editor"),
    ;

    /**
     * 条件
     */
    private final String type;

}
