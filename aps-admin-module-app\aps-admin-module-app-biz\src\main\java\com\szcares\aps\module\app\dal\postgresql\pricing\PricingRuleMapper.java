package com.szcares.aps.module.app.dal.postgresql.pricing;

import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.framework.mybatis.core.mapper.BaseMapperX;
import com.szcares.aps.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.szcares.aps.module.app.controller.admin.pricing.vo.PricingRulePageReqVO;
import com.szcares.aps.module.app.dal.dataobject.pricing.PricingRuleDO;
import org.apache.ibatis.annotations.*;

@Mapper
public interface PricingRuleMapper extends BaseMapperX<PricingRuleDO> {

    int insert(PricingRuleDO entity);

    @Update("UPDATE public.pricing_rules_extend" +
            " SET markup_rate = #{entity.markupRate}," +
            " updated_at = #{entity.updatedAt}" +
            " WHERE id = #{entity.id}")
    int updateById(@Param("entity") PricingRuleDO entity);

    @Delete("DELETE FROM public.pricing_rules_extend WHERE id = #{id}")
    int deleteById(Long id);

    default PageResult<PricingRuleDO> selectPage(PricingRulePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PricingRuleDO>()
                .eqIfPresent(PricingRuleDO::getModelName, reqVO.getModelName())
                .orderByDesc(PricingRuleDO::getModelName));
    }

    default PricingRuleDO selectById(Long id) {
        return selectOne(PricingRuleDO::getId, id);
    }
}