package com.szcares.aps.module.agent.convert.entryexit;

import com.szcares.aps.framework.common.pojo.PageResult;
import com.szcares.aps.module.agent.controller.admin.entryexit.vo.EntryExitExportExcelVO;
import com.szcares.aps.module.agent.controller.admin.entryexit.vo.EntryExitImportExcelVO;
import com.szcares.aps.module.agent.controller.admin.entryexit.vo.EntryExitRespVO;
import com.szcares.aps.module.agent.controller.admin.entryexit.vo.EntryExitSaveReqVO;
import com.szcares.aps.module.agent.dal.dataobject.entryexit.EntryExitDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 员工入职离职登记表 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface EntryExitConvert {

    EntryExitConvert INSTANCE = Mappers.getMapper(EntryExitConvert.class);

    EntryExitDO convert(EntryExitSaveReqVO bean);

    EntryExitRespVO convert(EntryExitDO bean);

//    List<EntryExitRespVO> convertList(List<EntryExitDO> list);

    PageResult<EntryExitRespVO> convertPage(PageResult<EntryExitDO> page);

    List<EntryExitDO> convertList(List<EntryExitImportExcelVO> list);

    List<EntryExitExportExcelVO> convertList02(List<EntryExitDO> list);

}