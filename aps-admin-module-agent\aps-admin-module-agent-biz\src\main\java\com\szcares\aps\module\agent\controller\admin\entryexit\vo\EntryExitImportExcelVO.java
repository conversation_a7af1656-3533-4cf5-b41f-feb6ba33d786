package com.szcares.aps.module.agent.controller.admin.entryexit.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 员工入职离职登记表 Excel 导入 VO
 *
 * <AUTHOR>
 */
@ExcelIgnoreUnannotated
@Data
public class EntryExitImportExcelVO implements Serializable {

    @ExcelProperty(value = "序号", index = 0)
    private String id;

    @ExcelProperty(value = "姓名", index = 1)
    private String employeeName;

    @ExcelProperty(index = 2)
    private String movementDate;

    /**
     * 变动类型：入职/离职
     * 这个字段不从Excel读取，而是根据sheet名称动态设置
     */
    private String movementType;

    /**
     * 年份
     * 这个字段不从Excel读取，而是根据sheet名称动态设置
     */
    private Integer year;

}