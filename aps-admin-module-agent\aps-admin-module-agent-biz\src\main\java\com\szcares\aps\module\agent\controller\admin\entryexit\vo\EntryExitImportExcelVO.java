package com.szcares.aps.module.agent.controller.admin.entryexit.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 员工入职离职登记表 Excel 导入 VO
 *
 * <AUTHOR>
 */
@ExcelIgnoreUnannotated
@Data
public class EntryExitImportExcelVO implements Serializable {

    @ExcelProperty(value = "序号",index = 0)
    private String id;

    @ExcelProperty(value = "姓名",index = 1)
    private String employeeName;

//    @ExcelProperty(value = "入职日期\n")
//    private String movementType;

    @ExcelProperty(value = "入职日期",index = 2)
    private String movementDate;

}